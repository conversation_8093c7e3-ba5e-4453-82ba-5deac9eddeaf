2025-08-09 07:34:56.097 [info] components database created cost 1 ms   
2025-08-09 07:34:56.097 [info] components index initializing...   
2025-08-09 07:34:56.333 [info] start to batch put pages: 5   
2025-08-09 07:34:56.336 [info] batch persist cost 5  3 
2025-08-09 07:34:56.388 [info] components index initialized, 1073 files cost 292 ms   
2025-08-09 07:34:56.388 [info] refresh page data from init listeners 0 1073   
2025-08-09 07:34:57.801 [info] indexing created file components/logs/2025-08-09.components.log  [object Object] 
2025-08-09 07:34:57.802 [info] refresh page data from created listeners 0 1074   
2025-08-09 07:34:57.922 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 07:34:58.182 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 07:34:58.521 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-09 07:34:58.534 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-09 07:34:58.538 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-09 07:34:58.541 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-09 07:35:37.056 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 07:35:37.822 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-09 07:35:37.832 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-09 07:35:37.835 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-09 07:35:37.839 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-09 07:37:56.019 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:37:56.037 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:37:56.039 [info] index finished after resolve  [object Object] 
2025-08-09 07:37:56.040 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:37:59.170 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:37:59.183 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:37:59.184 [info] index finished after resolve  [object Object] 
2025-08-09 07:37:59.184 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:38:03.009 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:38:03.033 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:38:03.034 [info] index finished after resolve  [object Object] 
2025-08-09 07:38:03.034 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:38:32.012 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:38:32.039 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:38:32.040 [info] index finished after resolve  [object Object] 
2025-08-09 07:38:32.041 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:38:34.194 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:38:34.210 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:38:34.211 [info] index finished after resolve  [object Object] 
2025-08-09 07:38:34.212 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:42:51.324 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:42:51.392 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:42:51.393 [info] index finished after resolve  [object Object] 
2025-08-09 07:42:51.394 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:43:01.850 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:43:01.872 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:43:01.874 [info] index finished after resolve  [object Object] 
2025-08-09 07:43:01.875 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:43:05.245 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:43:05.268 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:43:05.271 [info] index finished after resolve  [object Object] 
2025-08-09 07:43:05.271 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:43:09.284 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:43:09.291 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:43:09.292 [info] index finished after resolve  [object Object] 
2025-08-09 07:43:09.292 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:43:11.344 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:43:11.349 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:43:11.351 [info] index finished after resolve  [object Object] 
2025-08-09 07:43:11.351 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:43:13.449 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:43:13.454 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:43:13.457 [info] index finished after resolve  [object Object] 
2025-08-09 07:43:13.457 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:44:11.698 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:44:11.702 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:44:11.704 [info] index finished after resolve  [object Object] 
2025-08-09 07:44:11.704 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:44:14.541 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:44:14.544 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:44:14.545 [info] index finished after resolve  [object Object] 
2025-08-09 07:44:14.546 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:44:17.893 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:44:17.897 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:44:17.898 [info] index finished after resolve  [object Object] 
2025-08-09 07:44:17.899 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:44:21.613 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:44:21.617 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:44:21.618 [info] index finished after resolve  [object Object] 
2025-08-09 07:44:21.618 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:44:26.422 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:44:26.429 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:44:26.430 [info] index finished after resolve  [object Object] 
2025-08-09 07:44:26.430 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:44:56.714 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:44:56.718 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:44:56.718 [info] index finished after resolve  [object Object] 
2025-08-09 07:44:56.719 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:44:58.865 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:44:58.868 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:44:58.868 [info] index finished after resolve  [object Object] 
2025-08-09 07:44:58.869 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:01.232 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:01.237 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:01.237 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:01.238 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:05.163 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:05.167 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:05.168 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:05.169 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:07.315 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:07.322 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:07.322 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:07.323 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:09.394 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:09.398 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:09.399 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:09.399 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:12.036 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:12.042 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:12.043 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:12.044 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:16.600 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:16.604 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:16.604 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:16.605 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:18.990 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:18.995 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:18.995 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:18.996 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:22.540 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:22.545 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:22.546 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:22.546 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:24.797 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:24.802 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:24.802 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:24.803 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:32.718 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:32.725 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:32.725 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:32.726 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:45:58.854 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:45:58.857 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:45:58.858 [info] index finished after resolve  [object Object] 
2025-08-09 07:45:58.859 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:01.310 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:01.314 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:01.315 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:01.315 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:10.275 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:10.279 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:10.280 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:10.281 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:14.278 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:14.281 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:14.282 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:14.282 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:16.881 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:16.884 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:16.885 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:16.885 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:19.363 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:19.366 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:19.367 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:19.367 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:24.427 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:24.430 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:24.432 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:24.432 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:27.036 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:27.040 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:27.040 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:27.041 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:31.249 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:31.272 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:31.275 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:31.276 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:32.926 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:32.930 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:32.931 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:32.931 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:49.540 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:49.544 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:49.544 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:49.545 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:53.480 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:53.484 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:53.485 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:53.485 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:55.632 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:55.635 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:55.636 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:55.637 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:57.632 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:57.636 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:57.637 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:57.637 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:46:59.740 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:46:59.744 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:46:59.744 [info] index finished after resolve  [object Object] 
2025-08-09 07:46:59.745 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:02.969 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:02.974 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:02.976 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:02.976 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:05.072 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:05.075 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:05.077 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:05.077 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:08.100 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:08.103 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:08.104 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:08.105 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:10.426 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:10.431 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:10.432 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:10.433 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:14.466 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:14.471 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:14.472 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:14.472 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:18.346 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:18.349 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:18.351 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:18.352 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:20.508 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:20.510 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:20.511 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:20.511 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:22.751 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:22.756 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:22.756 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:22.757 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:25.365 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:25.369 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:25.370 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:25.370 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:27.457 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:27.461 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:27.462 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:27.462 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:29.814 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:29.819 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:29.821 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:29.821 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:32.460 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:32.465 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:32.465 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:32.465 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:34.784 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:34.788 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:34.789 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:34.789 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:43.384 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:43.388 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:43.389 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:43.389 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:45.474 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:45.478 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:45.479 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:45.479 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:47.689 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:47.694 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:47.695 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:47.696 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:49.911 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:49.915 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:49.916 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:49.916 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:52.047 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:52.050 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:52.051 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:52.051 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:54.084 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:54.088 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:54.088 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:54.088 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:47:57.884 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:47:57.887 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:47:57.888 [info] index finished after resolve  [object Object] 
2025-08-09 07:47:57.889 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:02.771 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:02.775 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:02.776 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:02.776 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:04.932 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:04.936 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:04.937 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:04.937 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:17.605 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:17.609 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:17.610 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:17.611 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:19.703 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:19.707 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:19.707 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:19.708 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:21.738 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:21.742 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:21.743 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:21.744 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:24.040 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:24.047 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:24.048 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:24.048 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:26.130 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:26.135 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:26.136 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:26.136 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:28.258 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:28.263 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:28.265 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:28.265 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:30.343 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:30.348 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:30.348 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:30.349 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:34.350 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:34.354 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:34.356 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:34.356 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:38.951 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:38.955 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:38.957 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:38.957 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:41.102 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:41.106 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:41.108 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:41.108 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:43.770 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:43.774 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:43.775 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:43.775 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:46.126 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:46.129 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:46.130 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:46.130 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:48.989 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:48.994 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:48.995 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:48.995 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:52.460 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:52.466 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:52.466 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:52.467 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:56.408 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:56.414 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:56.415 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:56.415 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:48:58.629 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:48:58.632 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:48:58.633 [info] index finished after resolve  [object Object] 
2025-08-09 07:48:58.633 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:02.756 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:02.761 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:02.762 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:02.762 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:08.033 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:08.036 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:08.037 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:08.037 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:10.562 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:10.567 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:10.567 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:10.568 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:15.563 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:15.567 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:15.568 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:15.568 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:17.679 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:17.684 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:17.686 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:17.686 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:19.795 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:19.799 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:19.800 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:19.800 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:24.972 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:24.978 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:24.980 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:24.980 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:30.294 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:30.298 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:30.299 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:30.300 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:33.232 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:33.237 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:33.237 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:33.238 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:39.533 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:39.536 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:39.537 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:39.537 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:44.452 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:44.456 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:44.457 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:44.457 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:46.505 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:46.509 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:46.510 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:46.510 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:49:52.955 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:49:52.957 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:49:52.958 [info] index finished after resolve  [object Object] 
2025-08-09 07:49:52.958 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:00.286 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:00.290 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:00.291 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:00.291 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:03.647 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:03.650 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:03.650 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:03.651 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:09.621 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:09.625 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:09.626 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:09.626 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:12.393 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:12.397 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:12.398 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:12.398 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:14.465 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:14.468 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:14.469 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:14.469 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:17.065 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:17.068 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:17.070 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:17.070 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:21.958 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:21.963 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:21.964 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:21.964 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:24.365 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:24.369 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:24.370 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:24.370 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:50:26.434 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:50:26.439 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:50:26.440 [info] index finished after resolve  [object Object] 
2025-08-09 07:50:26.440 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:52:04.251 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:52:04.259 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:52:04.259 [info] index finished after resolve  [object Object] 
2025-08-09 07:52:04.260 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:52:07.651 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:52:07.658 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:52:07.659 [info] index finished after resolve  [object Object] 
2025-08-09 07:52:07.659 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:07.662 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:07.671 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:07.673 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:07.673 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:20.007 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:20.012 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:20.012 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:20.012 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:25.156 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:25.163 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:25.164 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:25.165 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:27.712 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:27.722 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:27.722 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:27.723 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:31.306 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:31.310 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:31.311 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:31.311 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:35.855 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:35.863 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:35.864 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:35.864 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:39.915 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:39.922 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:39.927 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:39.927 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:55:42.043 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:55:42.050 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:55:42.051 [info] index finished after resolve  [object Object] 
2025-08-09 07:55:42.052 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:00.147 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:00.156 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:00.157 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:00.157 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:02.460 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:02.467 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:02.468 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:02.468 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:05.208 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:05.219 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:05.219 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:05.220 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:11.469 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:11.475 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:11.475 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:11.476 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:14.217 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:14.224 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:14.225 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:14.225 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:17.178 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:17.186 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:17.191 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:17.191 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:20.552 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:20.563 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:20.564 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:20.564 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:23.435 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:23.448 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:23.449 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:23.449 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:32.010 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:32.019 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:32.024 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:32.024 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:34.153 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:34.160 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:34.160 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:34.161 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:40.841 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:40.851 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:40.851 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:40.852 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:47.298 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:47.310 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:47.310 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:47.311 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:49.984 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:49.992 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:49.993 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:49.993 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:53.418 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:53.429 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:53.429 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:53.430 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:56:56.466 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:56:56.478 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:56:56.479 [info] index finished after resolve  [object Object] 
2025-08-09 07:56:56.479 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:00.402 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:00.410 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:00.411 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:00.411 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:02.533 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:02.544 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:02.544 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:02.545 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:06.466 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:06.476 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:06.478 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:06.478 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:12.575 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:12.587 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:12.588 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:12.588 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:19.979 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:19.986 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:19.986 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:19.987 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:22.470 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:22.476 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:22.476 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:22.476 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:25.926 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:25.932 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:25.932 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:25.933 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:35.292 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:35.300 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:35.300 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:35.301 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:38.260 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:38.268 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:38.268 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:38.269 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:57:41.143 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:57:41.150 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:57:41.151 [info] index finished after resolve  [object Object] 
2025-08-09 07:57:41.151 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:58:43.817 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:58:43.824 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:58:43.825 [info] index finished after resolve  [object Object] 
2025-08-09 07:58:43.825 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:58:46.082 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:58:46.087 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:58:46.088 [info] index finished after resolve  [object Object] 
2025-08-09 07:58:46.088 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:58:48.335 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:58:48.343 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:58:48.344 [info] index finished after resolve  [object Object] 
2025-08-09 07:58:48.345 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:58:50.502 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:58:50.511 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:58:50.512 [info] index finished after resolve  [object Object] 
2025-08-09 07:58:50.513 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:58:52.596 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:58:52.604 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:58:52.604 [info] index finished after resolve  [object Object] 
2025-08-09 07:58:52.604 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:58:55.426 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:58:55.436 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:58:55.436 [info] index finished after resolve  [object Object] 
2025-08-09 07:58:55.437 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:59:36.671 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:59:36.680 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:59:36.681 [info] index finished after resolve  [object Object] 
2025-08-09 07:59:36.681 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:59:40.016 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:59:40.027 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:59:40.027 [info] index finished after resolve  [object Object] 
2025-08-09 07:59:40.028 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:59:42.854 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:59:42.862 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:59:42.862 [info] index finished after resolve  [object Object] 
2025-08-09 07:59:42.863 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 07:59:45.326 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 07:59:45.336 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 07:59:45.337 [info] index finished after resolve  [object Object] 
2025-08-09 07:59:45.338 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:00:20.775 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:00:20.782 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:00:20.783 [info] index finished after resolve  [object Object] 
2025-08-09 08:00:20.783 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:00:23.082 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:00:23.097 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:00:23.098 [info] index finished after resolve  [object Object] 
2025-08-09 08:00:23.098 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:06:43.138 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:06:43.145 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:06:43.146 [info] index finished after resolve  [object Object] 
2025-08-09 08:06:43.146 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:06:48.306 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:06:48.312 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:06:48.313 [info] index finished after resolve  [object Object] 
2025-08-09 08:06:48.313 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:06:51.747 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:06:51.755 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:06:51.756 [info] index finished after resolve  [object Object] 
2025-08-09 08:06:51.756 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:06:56.999 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:06:57.008 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:06:57.009 [info] index finished after resolve  [object Object] 
2025-08-09 08:06:57.009 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:06:59.426 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:06:59.438 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:06:59.438 [info] index finished after resolve  [object Object] 
2025-08-09 08:06:59.439 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:07:02.227 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:07:02.239 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:07:02.239 [info] index finished after resolve  [object Object] 
2025-08-09 08:07:02.240 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:07:05.661 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:07:05.674 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:07:05.675 [info] index finished after resolve  [object Object] 
2025-08-09 08:07:05.675 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:10.531 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:10.540 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:10.540 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:10.541 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:18.707 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:18.718 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:18.719 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:18.720 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:21.580 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:21.592 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:21.593 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:21.593 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:23.645 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:23.657 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:23.658 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:23.658 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:29.014 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:29.025 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:29.026 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:29.026 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:31.901 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:31.913 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:31.913 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:31.913 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:35.112 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:35.122 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:35.122 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:35.123 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:38.743 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:38.750 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:38.751 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:38.751 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:42.052 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:42.062 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:42.063 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:42.063 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:45.899 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:45.910 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:45.911 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:45.911 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:48.893 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:48.901 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:48.901 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:48.901 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:51.084 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:51.092 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:51.093 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:51.093 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:54.497 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:54.508 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:54.510 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:54.510 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:57.293 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:57.299 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:57.300 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:57.300 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:08:59.811 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:08:59.818 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:08:59.822 [info] index finished after resolve  [object Object] 
2025-08-09 08:08:59.823 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:03.278 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:03.289 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:03.290 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:03.290 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:06.052 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:06.062 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:06.062 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:06.063 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:08.167 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:08.176 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:08.177 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:08.177 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:10.456 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:10.464 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:10.464 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:10.464 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:13.745 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:13.753 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:13.757 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:13.758 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:16.185 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:16.197 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:16.197 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:16.198 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:20.620 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:20.628 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:20.629 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:20.629 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:24.166 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:24.180 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:24.181 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:24.181 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:26.839 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:26.850 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:26.851 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:26.851 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:31.155 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:31.162 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:31.163 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:31.163 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:33.636 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:33.646 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:33.646 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:33.646 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:36.158 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:36.165 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:36.166 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:36.166 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:38.192 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:38.202 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:38.202 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:38.202 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:41.522 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:41.528 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:41.529 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:41.529 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:43.659 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:43.666 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:43.666 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:43.666 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:47.042 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:47.049 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:47.050 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:47.050 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:52.817 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:52.828 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:52.828 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:52.829 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:09:58.557 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:09:58.564 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:09:58.565 [info] index finished after resolve  [object Object] 
2025-08-09 08:09:58.565 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:01.801 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:01.808 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:01.808 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:01.809 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:07.640 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:07.649 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:07.649 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:07.649 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:12.576 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:12.584 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:12.585 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:12.585 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:14.565 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:14.575 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:14.576 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:14.576 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:19.977 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:19.985 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:19.986 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:19.986 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:22.539 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:22.548 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:22.549 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:22.549 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:26.021 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:26.028 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:26.028 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:26.029 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:28.117 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:28.124 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:28.128 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:28.128 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:50.134 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:50.150 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:50.151 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:50.151 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:54.449 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:54.471 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:54.475 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:54.476 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:56.476 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:56.485 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:56.486 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:56.486 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:10:59.952 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:10:59.963 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:10:59.963 [info] index finished after resolve  [object Object] 
2025-08-09 08:10:59.964 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:02.002 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:02.008 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:02.014 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:02.014 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:04.201 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:04.211 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:04.212 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:04.212 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:07.509 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:07.520 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:07.521 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:07.522 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:14.175 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:14.183 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:14.183 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:14.183 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:17.216 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:17.240 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:17.241 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:17.242 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:19.235 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:19.243 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:19.244 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:19.244 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:21.330 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:21.337 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:21.337 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:21.337 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:23.429 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:23.439 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:23.440 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:23.440 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:27.347 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:27.354 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:27.354 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:27.355 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:30.233 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:30.246 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:30.247 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:30.248 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:32.285 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:32.296 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:32.296 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:32.297 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:34.307 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:34.317 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:34.317 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:34.317 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:36.549 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:36.559 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:36.560 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:36.560 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:38.636 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:38.645 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:38.646 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:38.646 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:40.720 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:40.732 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:40.733 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:40.733 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:43.162 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:43.191 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:43.194 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:43.195 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:45.189 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:45.197 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:45.197 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:45.198 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:47.354 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:47.362 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:47.363 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:47.363 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:49.445 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:49.454 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:49.455 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:49.455 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:51.622 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:51.631 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:51.632 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:51.632 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:11:54.295 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:11:54.303 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:11:54.303 [info] index finished after resolve  [object Object] 
2025-08-09 08:11:54.303 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:06.216 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:06.227 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:06.229 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:06.229 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:08.401 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:08.414 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:08.415 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:08.416 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:11.495 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:11.502 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:11.503 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:11.503 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:13.574 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:13.584 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:13.584 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:13.585 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:26.456 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:26.464 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:26.470 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:26.471 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:30.613 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:30.620 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:30.620 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:30.620 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:33.929 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:33.939 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:33.940 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:33.940 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:36.353 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:36.361 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:36.369 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:36.369 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:39.617 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:39.628 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:39.629 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:39.629 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:41.807 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:41.814 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:41.815 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:41.815 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:45.087 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:45.097 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:45.097 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:45.098 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:47.929 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:47.939 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:47.939 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:47.940 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:12:58.188 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:12:58.194 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:12:58.195 [info] index finished after resolve  [object Object] 
2025-08-09 08:12:58.195 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:13:01.548 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:13:01.556 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:13:01.556 [info] index finished after resolve  [object Object] 
2025-08-09 08:13:01.557 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:14:19.001 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:14:19.007 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:14:19.009 [info] index finished after resolve  [object Object] 
2025-08-09 08:14:19.009 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:14:23.422 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:14:23.430 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:14:23.431 [info] index finished after resolve  [object Object] 
2025-08-09 08:14:23.431 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:14:26.920 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:14:26.930 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:14:26.933 [info] index finished after resolve  [object Object] 
2025-08-09 08:14:26.934 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:14:34.923 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:14:34.931 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:14:34.934 [info] index finished after resolve  [object Object] 
2025-08-09 08:14:34.935 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:14:59.417 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:14:59.432 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:14:59.433 [info] index finished after resolve  [object Object] 
2025-08-09 08:14:59.433 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:17:26.904 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:17:26.917 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:17:26.918 [info] index finished after resolve  [object Object] 
2025-08-09 08:17:26.918 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:17:54.477 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:17:54.485 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:17:54.486 [info] index finished after resolve  [object Object] 
2025-08-09 08:17:54.486 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:17:56.954 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:17:56.961 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:17:56.962 [info] index finished after resolve  [object Object] 
2025-08-09 08:17:56.962 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:18:02.694 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:18:02.704 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:18:02.705 [info] index finished after resolve  [object Object] 
2025-08-09 08:18:02.705 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:18:06.972 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:18:06.982 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:18:06.983 [info] index finished after resolve  [object Object] 
2025-08-09 08:18:06.983 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:18:13.094 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:18:13.104 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:18:13.105 [info] index finished after resolve  [object Object] 
2025-08-09 08:18:13.105 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:18:15.410 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:18:15.417 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:18:15.418 [info] index finished after resolve  [object Object] 
2025-08-09 08:18:15.418 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:03.676 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:03.687 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:03.688 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:03.688 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:07.211 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:07.221 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:07.221 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:07.222 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:10.280 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:10.293 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:10.294 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:10.294 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:12.797 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:12.806 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:12.807 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:12.807 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:16.644 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:16.655 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:16.656 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:16.656 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:21.984 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:21.989 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:21.990 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:21.990 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:24.698 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:24.705 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:24.709 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:24.709 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:27.015 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:27.026 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:27.027 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:27.027 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:29.187 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:29.195 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:29.195 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:29.196 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:19:49.222 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:19:49.234 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:19:49.235 [info] index finished after resolve  [object Object] 
2025-08-09 08:19:49.236 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:20:54.348 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:20:54.355 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:20:54.359 [info] index finished after resolve  [object Object] 
2025-08-09 08:20:54.359 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:20:56.605 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:20:56.611 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:20:56.614 [info] index finished after resolve  [object Object] 
2025-08-09 08:20:56.614 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:20:58.769 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:20:58.780 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:20:58.780 [info] index finished after resolve  [object Object] 
2025-08-09 08:20:58.781 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:02.082 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:02.094 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:02.095 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:02.095 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:04.124 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:04.129 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:04.132 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:04.132 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:22.751 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:22.761 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:22.761 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:22.762 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:26.477 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:26.485 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:26.489 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:26.489 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:39.092 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:39.098 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:39.101 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:39.101 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:42.047 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:42.058 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:42.059 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:42.059 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:44.367 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:44.374 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:44.375 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:44.375 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:48.073 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:48.080 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:48.082 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:48.082 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:51.672 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:51.679 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:51.681 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:51.681 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:21:54.194 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:21:54.201 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:21:54.204 [info] index finished after resolve  [object Object] 
2025-08-09 08:21:54.205 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:02.594 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:02.606 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:02.607 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:02.607 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:08.738 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:08.748 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:08.748 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:08.749 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:16.776 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:16.784 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:16.785 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:16.785 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:20.409 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:20.420 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:20.421 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:20.421 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:23.587 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:23.596 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:23.601 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:23.601 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:34.657 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:34.664 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:34.664 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:34.665 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:43.303 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:43.310 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:43.311 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:43.311 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:46.345 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:46.355 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:46.356 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:46.357 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:22:48.555 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:22:48.565 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:22:48.565 [info] index finished after resolve  [object Object] 
2025-08-09 08:22:48.565 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:23:13.052 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:23:13.064 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:23:13.065 [info] index finished after resolve  [object Object] 
2025-08-09 08:23:13.066 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:23:18.284 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:23:18.294 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:23:18.294 [info] index finished after resolve  [object Object] 
2025-08-09 08:23:18.295 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:23:21.438 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:23:21.444 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:23:21.448 [info] index finished after resolve  [object Object] 
2025-08-09 08:23:21.449 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:23:38.312 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:23:38.324 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:23:38.325 [info] index finished after resolve  [object Object] 
2025-08-09 08:23:38.326 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:23:40.482 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:23:40.493 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:23:40.494 [info] index finished after resolve  [object Object] 
2025-08-09 08:23:40.494 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:08.384 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:08.395 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:08.396 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:08.396 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:11.349 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:11.360 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:11.361 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:11.361 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:15.178 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:15.188 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:15.189 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:15.189 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:20.286 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:20.295 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:20.299 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:20.299 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:24.338 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:24.348 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:24.349 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:24.349 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:29.425 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:29.433 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:29.437 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:29.437 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:31.518 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:31.530 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:31.531 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:31.532 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:35.829 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:35.836 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:35.839 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:35.839 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:54.045 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:54.052 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:54.054 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:54.055 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:24:57.355 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:24:57.363 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:24:57.365 [info] index finished after resolve  [object Object] 
2025-08-09 08:24:57.366 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:05.589 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:05.597 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:05.598 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:05.598 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:10.305 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:10.317 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:10.320 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:10.320 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:12.298 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:12.308 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:12.310 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:12.310 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:27.124 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:27.132 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:27.135 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:27.135 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:29.350 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:29.358 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:29.361 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:29.362 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:37.261 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:37.269 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:37.271 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:37.272 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:40.290 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:40.298 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:40.300 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:40.300 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:42.280 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:42.290 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:42.291 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:42.292 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:47.964 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:47.974 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:47.975 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:47.976 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:50.616 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:50.625 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:50.628 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:50.629 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:55.160 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:55.166 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:55.167 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:55.167 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:25:57.590 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:25:57.599 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:25:57.599 [info] index finished after resolve  [object Object] 
2025-08-09 08:25:57.600 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:16.704 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:16.715 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:16.716 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:16.716 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:24.995 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:25.007 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:25.009 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:25.009 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:28.615 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:28.621 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:28.624 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:28.624 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:31.481 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:31.487 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:31.491 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:31.492 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:36.497 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:36.505 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:36.506 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:36.506 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:44.983 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:44.992 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:44.993 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:44.993 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:52.159 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:52.165 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:52.166 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:52.166 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:27:56.168 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:27:56.174 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:27:56.175 [info] index finished after resolve  [object Object] 
2025-08-09 08:27:56.175 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:28:00.903 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:28:00.913 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:28:00.914 [info] index finished after resolve  [object Object] 
2025-08-09 08:28:00.914 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:28:03.825 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:28:03.836 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:28:03.836 [info] index finished after resolve  [object Object] 
2025-08-09 08:28:03.836 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:28:06.150 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:28:06.158 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:28:06.159 [info] index finished after resolve  [object Object] 
2025-08-09 08:28:06.159 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:28:08.395 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:28:08.405 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:28:08.406 [info] index finished after resolve  [object Object] 
2025-08-09 08:28:08.406 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:28:11.335 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:28:11.342 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:28:11.345 [info] index finished after resolve  [object Object] 
2025-08-09 08:28:11.346 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:28:19.769 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:28:19.775 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:28:19.779 [info] index finished after resolve  [object Object] 
2025-08-09 08:28:19.780 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:28:22.429 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:28:22.438 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:28:22.442 [info] index finished after resolve  [object Object] 
2025-08-09 08:28:22.443 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:33:20.106 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:33:20.112 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:33:20.114 [info] index finished after resolve  [object Object] 
2025-08-09 08:33:20.115 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:33:24.083 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:33:24.091 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:33:24.092 [info] index finished after resolve  [object Object] 
2025-08-09 08:33:24.093 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:33:28.546 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:33:28.558 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:33:28.559 [info] index finished after resolve  [object Object] 
2025-08-09 08:33:28.559 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:33:31.020 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:33:31.029 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:33:31.030 [info] index finished after resolve  [object Object] 
2025-08-09 08:33:31.030 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:33:33.224 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:33:33.233 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:33:33.234 [info] index finished after resolve  [object Object] 
2025-08-09 08:33:33.234 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:35:58.809 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:35:58.820 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:35:58.821 [info] index finished after resolve  [object Object] 
2025-08-09 08:35:58.821 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:01.495 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:01.508 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:01.509 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:01.509 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:06.497 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:06.504 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:06.509 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:06.509 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:08.662 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:08.669 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:08.670 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:08.671 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:10.825 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:10.831 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:10.832 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:10.832 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:17.877 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:17.887 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:17.888 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:17.889 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:19.943 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:19.953 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:19.954 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:19.954 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:28.959 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:28.966 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:28.967 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:28.968 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:36:31.141 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:36:31.148 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:36:31.152 [info] index finished after resolve  [object Object] 
2025-08-09 08:36:31.152 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:00.912 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:00.918 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:00.918 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:00.919 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:11.799 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:11.805 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:11.805 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:11.806 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:15.939 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:15.947 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:15.948 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:15.948 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:21.595 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:21.601 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:21.601 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:21.602 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:27.446 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:27.457 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:27.459 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:27.459 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:30.319 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:30.327 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:30.328 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:30.328 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:36.338 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:36.349 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:36.350 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:36.350 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:40.611 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:40.618 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:40.619 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:40.619 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:45.090 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:45.099 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:45.099 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:45.100 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:37:48.195 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:37:48.207 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:37:48.208 [info] index finished after resolve  [object Object] 
2025-08-09 08:37:48.208 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:38:27.835 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:38:27.847 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:38:27.848 [info] index finished after resolve  [object Object] 
2025-08-09 08:38:27.848 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:38:31.292 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:38:31.299 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:38:31.299 [info] index finished after resolve  [object Object] 
2025-08-09 08:38:31.300 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:38:39.052 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:38:39.063 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:38:39.063 [info] index finished after resolve  [object Object] 
2025-08-09 08:38:39.064 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:38:42.613 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:38:42.623 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:38:42.624 [info] index finished after resolve  [object Object] 
2025-08-09 08:38:42.624 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:38:48.464 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:38:48.473 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:38:48.474 [info] index finished after resolve  [object Object] 
2025-08-09 08:38:48.474 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:39:00.148 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:39:00.155 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:39:00.156 [info] index finished after resolve  [object Object] 
2025-08-09 08:39:00.156 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:39:03.948 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:39:03.956 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:39:03.957 [info] index finished after resolve  [object Object] 
2025-08-09 08:39:03.957 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:39:09.413 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:39:09.418 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:39:09.419 [info] index finished after resolve  [object Object] 
2025-08-09 08:39:09.419 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:39:18.232 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:39:18.238 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:39:18.239 [info] index finished after resolve  [object Object] 
2025-08-09 08:39:18.240 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:42:22.416 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:42:22.422 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:42:22.423 [info] index finished after resolve  [object Object] 
2025-08-09 08:42:22.423 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:42:25.262 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:42:25.269 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:42:25.275 [info] index finished after resolve  [object Object] 
2025-08-09 08:42:25.275 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:43:34.091 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:43:34.102 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:43:34.103 [info] index finished after resolve  [object Object] 
2025-08-09 08:43:34.103 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:43:37.475 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:43:37.484 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:43:37.485 [info] index finished after resolve  [object Object] 
2025-08-09 08:43:37.485 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:43:45.301 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:43:45.308 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:43:45.309 [info] index finished after resolve  [object Object] 
2025-08-09 08:43:45.309 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:43:50.644 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:43:50.652 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:43:50.653 [info] index finished after resolve  [object Object] 
2025-08-09 08:43:50.654 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:43:54.622 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:43:54.630 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:43:54.630 [info] index finished after resolve  [object Object] 
2025-08-09 08:43:54.631 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:43:57.075 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:43:57.088 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:43:57.090 [info] index finished after resolve  [object Object] 
2025-08-09 08:43:57.090 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:44:02.358 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:44:02.369 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:44:02.370 [info] index finished after resolve  [object Object] 
2025-08-09 08:44:02.370 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:44:05.634 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:44:05.641 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:44:05.642 [info] index finished after resolve  [object Object] 
2025-08-09 08:44:05.642 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:44:16.484 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:44:16.496 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:44:16.497 [info] index finished after resolve  [object Object] 
2025-08-09 08:44:16.497 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:44:27.263 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:44:27.273 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:44:27.273 [info] index finished after resolve  [object Object] 
2025-08-09 08:44:27.274 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:44:34.263 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:44:34.270 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:44:34.275 [info] index finished after resolve  [object Object] 
2025-08-09 08:44:34.275 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:44:41.652 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:44:41.658 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:44:41.659 [info] index finished after resolve  [object Object] 
2025-08-09 08:44:41.660 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:44:48.462 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:44:48.473 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:44:48.473 [info] index finished after resolve  [object Object] 
2025-08-09 08:44:48.473 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:03.676 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:03.686 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:03.687 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:03.687 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:08.503 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:08.509 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:08.509 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:08.510 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:11.890 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:11.896 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:11.897 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:11.897 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:22.391 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:22.401 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:22.402 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:22.402 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:24.475 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:24.481 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:24.482 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:24.483 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:30.317 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:30.325 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:30.326 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:30.327 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:40.603 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:40.608 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:40.609 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:40.609 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:45:42.731 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:45:42.741 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:45:42.742 [info] index finished after resolve  [object Object] 
2025-08-09 08:45:42.742 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:46:07.857 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:46:07.864 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:46:07.864 [info] index finished after resolve  [object Object] 
2025-08-09 08:46:07.865 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:46:09.990 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:46:09.998 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:46:10.005 [info] index finished after resolve  [object Object] 
2025-08-09 08:46:10.006 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:46:30.140 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:46:30.149 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:46:30.150 [info] index finished after resolve  [object Object] 
2025-08-09 08:46:30.150 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:46:32.370 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:46:32.377 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:46:32.378 [info] index finished after resolve  [object Object] 
2025-08-09 08:46:32.378 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:46:34.500 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:46:34.508 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:46:34.509 [info] index finished after resolve  [object Object] 
2025-08-09 08:46:34.509 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:46:36.963 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:46:36.974 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:46:36.974 [info] index finished after resolve  [object Object] 
2025-08-09 08:46:36.975 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:33.756 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:33.766 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:33.767 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:33.767 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:35.804 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:35.811 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:35.811 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:35.812 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:37.945 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:37.956 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:37.958 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:37.958 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:40.152 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:40.160 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:40.161 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:40.162 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:42.781 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:42.791 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:42.792 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:42.792 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:48.666 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:48.677 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:48.678 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:48.678 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:52.181 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:52.187 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:52.191 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:52.192 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:55.499 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:55.509 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:55.510 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:55.510 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:47:57.558 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:47:57.568 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:47:57.568 [info] index finished after resolve  [object Object] 
2025-08-09 08:47:57.568 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:48:00.578 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:00.586 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:00.586 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:00.586 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:48:03.918 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:03.931 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:03.931 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:03.932 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:48:06.417 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:06.424 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:06.425 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:06.425 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:48:19.527 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:19.567 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:19.568 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:19.569 [info] refresh page data from resolve listeners 0 1074   
2025-08-09 08:48:42.068 [info] indexing created file 学习库/Deep learning/概念库/attachments/神经网络-2025-08-09,08-08,08-48-42.png  [object Object] 
2025-08-09 08:48:42.069 [info] refresh page data from created listeners 0 1075   
2025-08-09 08:48:44.106 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:44.119 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:44.120 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:44.120 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:48:49.170 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:49.184 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:49.186 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:49.186 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:48:51.374 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:51.452 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:51.452 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:51.453 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:48:53.412 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:53.424 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:53.424 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:53.425 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:48:57.152 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:57.162 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:57.165 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:57.166 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:48:59.468 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:48:59.479 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:48:59.480 [info] index finished after resolve  [object Object] 
2025-08-09 08:48:59.480 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:49:32.367 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:49:32.462 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:49:32.464 [info] index finished after resolve  [object Object] 
2025-08-09 08:49:32.465 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:49:41.727 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:49:41.735 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:49:41.735 [info] index finished after resolve  [object Object] 
2025-08-09 08:49:41.735 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:49:43.943 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:49:43.949 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:49:43.950 [info] index finished after resolve  [object Object] 
2025-08-09 08:49:43.950 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:49:46.216 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:49:46.228 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:49:46.229 [info] index finished after resolve  [object Object] 
2025-08-09 08:49:46.229 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:49:48.270 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:49:48.280 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:49:48.280 [info] index finished after resolve  [object Object] 
2025-08-09 08:49:48.281 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:49:50.414 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:49:50.426 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:49:50.427 [info] index finished after resolve  [object Object] 
2025-08-09 08:49:50.427 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:04.095 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:04.105 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:04.109 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:04.109 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:07.370 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:07.382 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:07.383 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:07.383 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:11.168 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:11.180 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:11.181 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:11.181 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:13.899 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:13.913 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:13.914 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:13.914 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:18.310 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:18.319 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:18.322 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:18.322 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:20.528 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:20.538 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:20.538 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:20.539 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:36.673 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:36.681 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:36.683 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:36.684 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:44.605 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:44.613 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:44.614 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:44.614 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:47.180 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:47.190 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:47.192 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:47.192 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:49.623 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:49.632 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:49.635 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:49.635 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:51.767 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:51.779 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:51.780 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:51.780 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:50:54.163 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:50:54.173 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:50:54.178 [info] index finished after resolve  [object Object] 
2025-08-09 08:50:54.178 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:02.065 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:02.076 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:02.077 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:02.077 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:04.180 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:04.192 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:04.194 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:04.194 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:06.230 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:06.243 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:06.243 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:06.244 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:08.586 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:08.599 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:08.599 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:08.599 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:10.696 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:10.703 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:10.703 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:10.703 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:12.781 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:12.789 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:12.789 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:12.790 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:23.450 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:23.461 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:23.462 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:23.462 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:27.131 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:27.140 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:27.141 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:27.141 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:29.189 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:29.202 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:29.202 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:29.203 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:35.618 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:35.629 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:35.630 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:35.630 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:37.705 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:37.717 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:37.718 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:37.718 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:40.502 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:40.514 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:40.515 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:40.515 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:51:42.631 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:51:42.642 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:51:42.643 [info] index finished after resolve  [object Object] 
2025-08-09 08:51:42.643 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:53:45.138 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:53:45.152 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:53:45.152 [info] index finished after resolve  [object Object] 
2025-08-09 08:53:45.153 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:54:18.449 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:54:18.461 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:54:18.465 [info] index finished after resolve  [object Object] 
2025-08-09 08:54:18.466 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:54:23.984 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:54:24.018 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:54:24.019 [info] index finished after resolve  [object Object] 
2025-08-09 08:54:24.019 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:54:25.565 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:54:25.575 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:54:25.576 [info] index finished after resolve  [object Object] 
2025-08-09 08:54:25.576 [info] refresh page data from resolve listeners 0 1075   
2025-08-09 08:54:38.679 [info] indexing created file 学习库/Deep learning/概念库/attachments/神经网络-2025-08-09,08-08,08-54-38.png  [object Object] 
2025-08-09 08:54:38.714 [info] refresh page data from created listeners 0 1076   
2025-08-09 08:54:40.716 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:54:40.725 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:54:40.729 [info] index finished after resolve  [object Object] 
2025-08-09 08:54:40.729 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:55:29.441 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:55:29.454 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:55:29.455 [info] index finished after resolve  [object Object] 
2025-08-09 08:55:29.455 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:55:31.692 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:55:31.707 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:55:31.708 [info] index finished after resolve  [object Object] 
2025-08-09 08:55:31.708 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:55:36.488 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:55:36.504 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:55:36.505 [info] index finished after resolve  [object Object] 
2025-08-09 08:55:36.505 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:55:38.707 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:55:38.723 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:55:38.724 [info] index finished after resolve  [object Object] 
2025-08-09 08:55:38.724 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:55:40.907 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:55:40.921 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:55:40.922 [info] index finished after resolve  [object Object] 
2025-08-09 08:55:40.923 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:55:56.662 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:55:56.702 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:55:56.703 [info] index finished after resolve  [object Object] 
2025-08-09 08:55:56.704 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:00.158 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:00.165 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:00.172 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:00.173 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:16.848 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:16.864 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:16.867 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:16.867 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:19.092 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:19.103 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:19.103 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:19.103 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:21.241 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:21.272 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:21.273 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:21.273 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:23.254 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:23.274 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:23.275 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:23.276 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:33.722 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:33.735 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:33.736 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:33.736 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:35.776 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:35.790 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:35.790 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:35.791 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:37.934 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:37.944 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:37.945 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:37.945 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:40.239 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:40.254 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:40.255 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:40.256 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:50.322 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:50.337 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:50.339 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:50.339 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:52.601 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:52.607 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:52.609 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:52.609 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:56:58.644 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:56:58.658 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:56:58.659 [info] index finished after resolve  [object Object] 
2025-08-09 08:56:58.659 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:58:54.192 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:58:54.199 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:58:54.201 [info] index finished after resolve  [object Object] 
2025-08-09 08:58:54.201 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:58:56.288 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:58:56.295 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:58:56.297 [info] index finished after resolve  [object Object] 
2025-08-09 08:58:56.297 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:58:59.041 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:58:59.047 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:58:59.048 [info] index finished after resolve  [object Object] 
2025-08-09 08:58:59.048 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:59:01.269 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:59:01.276 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:59:01.277 [info] index finished after resolve  [object Object] 
2025-08-09 08:59:01.277 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:59:04.309 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:59:04.316 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:59:04.317 [info] index finished after resolve  [object Object] 
2025-08-09 08:59:04.317 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:59:17.598 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:59:17.606 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:59:17.607 [info] index finished after resolve  [object Object] 
2025-08-09 08:59:17.607 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 08:59:20.839 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络.md   
2025-08-09 08:59:20.847 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-08-09 08:59:20.847 [info] index finished after resolve  [object Object] 
2025-08-09 08:59:20.847 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:29.226 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:29.400 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:29.407 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:29.408 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:31.535 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:31.542 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:31.542 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:31.543 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:33.633 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:33.829 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:33.831 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:33.832 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:36.200 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:36.214 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:36.216 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:36.216 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:38.661 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:38.670 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:38.672 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:38.673 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:40.699 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:40.709 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:40.709 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:40.710 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:42.897 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:43.110 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:43.112 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:43.112 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:01:52.847 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:01:53.043 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:01:53.043 [info] index finished after resolve  [object Object] 
2025-08-09 09:01:53.044 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:00.838 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:01.018 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:01.021 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:01.022 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:04.086 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:04.097 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:04.098 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:04.098 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:06.224 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:06.236 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:06.237 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:06.237 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:08.729 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:08.736 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:08.736 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:08.737 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:11.899 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:11.912 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:11.913 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:11.913 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:13.945 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:13.957 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:13.958 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:13.958 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:16.267 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:16.280 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:16.281 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:16.281 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:23.613 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:23.626 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:23.627 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:23.627 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:25.964 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:25.974 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:25.975 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:25.975 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:28.155 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:28.166 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:28.168 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:28.168 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:31.434 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:31.446 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:31.449 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:31.449 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:33.513 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:33.525 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:33.525 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:33.526 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:35.986 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:35.997 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:35.998 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:35.998 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:38.483 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:38.490 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:38.491 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:38.491 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:40.526 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:40.538 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:40.539 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:40.539 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:42.602 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:42.618 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:42.619 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:42.619 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:44.654 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:44.667 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:44.669 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:44.669 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:46.695 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:46.708 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:46.709 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:46.710 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:50.057 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:50.067 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:50.069 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:50.069 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:52.256 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:52.270 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:52.271 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:52.271 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:54.393 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:54.404 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:54.405 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:54.405 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:56.485 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:56.499 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:56.500 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:56.500 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:02:58.589 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:02:58.600 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:02:58.601 [info] index finished after resolve  [object Object] 
2025-08-09 09:02:58.602 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:02.871 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:02.878 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:02.882 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:02.882 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:04.926 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:04.937 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:04.938 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:04.938 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:07.430 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:07.442 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:07.444 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:07.444 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:09.470 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:09.481 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:09.483 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:09.483 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:12.631 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:12.642 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:12.643 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:12.643 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:14.910 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:14.920 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:14.922 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:14.922 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:18.686 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:18.700 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:18.700 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:18.701 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:20.879 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:20.891 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:20.893 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:20.893 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:23.428 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:23.439 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:23.440 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:23.440 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:27.107 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:27.119 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:27.121 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:27.121 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:29.707 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:29.714 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:29.719 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:29.719 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:34.203 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:34.490 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:34.501 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:34.502 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:03:43.293 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-03-43.png  [object Object] 
2025-08-09 09:03:43.295 [info] refresh page data from created listeners 0 1077   
2025-08-09 09:03:45.340 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:03:45.355 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:03:45.357 [info] index finished after resolve  [object Object] 
2025-08-09 09:03:45.358 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:04:43.953 [info] trigger 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-03-43.png resolve  [object Object] 
2025-08-09 09:04:43.991 [info] index finished after resolve  [object Object] 
2025-08-09 09:04:43.991 [info] refresh page data from modify listeners 0 1077   
2025-08-09 09:04:56.963 [info] refresh page data from rename listeners 0 1077   
2025-08-09 09:04:57.054 [info] trigger 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-03-43.webp resolve  [object Object] 
2025-08-09 09:04:57.104 [info] index finished after resolve  [object Object] 
2025-08-09 09:04:57.104 [info] refresh page data from modify listeners 0 1077   
2025-08-09 09:05:05.915 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:05:05.925 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:05:05.928 [info] index finished after resolve  [object Object] 
2025-08-09 09:05:05.929 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:05:08.706 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:05:08.718 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:05:08.720 [info] index finished after resolve  [object Object] 
2025-08-09 09:05:08.720 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:20:00.543 [info] trigger 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-46-01.png resolve  [object Object] 
2025-08-09 09:20:00.598 [info] index finished after resolve  [object Object] 
2025-08-09 09:20:00.599 [info] refresh page data from modify listeners 0 1077   
2025-08-09 09:20:41.816 [info] trigger 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-46-01.png resolve  [object Object] 
2025-08-09 09:20:41.868 [info] index finished after resolve  [object Object] 
2025-08-09 09:20:41.868 [info] refresh page data from modify listeners 0 1077   
2025-08-09 09:21:03.728 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:03.740 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:03.742 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:03.742 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:21:05.784 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:05.794 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:05.796 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:05.796 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:21:07.904 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:07.923 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:07.925 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:07.925 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:21:18.284 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:18.294 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:18.295 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:18.296 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:21:21.870 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:21.930 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:21.934 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:21.934 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:21:25.547 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:25.558 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:25.561 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:25.561 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:21:29.771 [info] refresh page data from delete listeners 0 1076   
2025-08-09 09:21:31.146 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:31.465 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:31.468 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:31.469 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:21:36.500 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:36.818 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:36.820 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:36.821 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:21:38.782 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:38.792 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:38.794 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:38.794 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:21:52.900 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:21:53.136 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:21:53.148 [info] index finished after resolve  [object Object] 
2025-08-09 09:21:53.148 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:22:40.957 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:22:40.970 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:22:40.971 [info] index finished after resolve  [object Object] 
2025-08-09 09:22:40.972 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:05.062 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:05.302 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:05.317 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:05.317 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:07.871 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:07.884 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:07.885 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:07.886 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:10.085 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:10.342 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:10.345 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:10.345 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:12.339 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:12.352 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:12.354 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:12.354 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:17.675 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:17.908 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:17.911 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:17.911 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:24.921 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:24.931 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:24.932 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:24.932 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:30.907 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:31.150 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:31.153 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:31.153 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:33.275 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:33.286 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:33.288 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:33.288 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:38.236 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:38.247 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:38.248 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:38.248 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:45.032 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:45.299 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:45.302 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:45.302 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:23:48.491 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:23:48.503 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:23:48.504 [info] index finished after resolve  [object Object] 
2025-08-09 09:23:48.504 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:24:14.890 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:24:14.901 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:24:14.902 [info] index finished after resolve  [object Object] 
2025-08-09 09:24:14.902 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:24:16.918 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:24:16.926 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:24:16.932 [info] index finished after resolve  [object Object] 
2025-08-09 09:24:16.933 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:24:20.464 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:24:20.476 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:24:20.478 [info] index finished after resolve  [object Object] 
2025-08-09 09:24:20.478 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:24:22.848 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:24:23.115 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:24:23.117 [info] index finished after resolve  [object Object] 
2025-08-09 09:24:23.117 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:24:25.081 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:24:25.092 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:24:25.093 [info] index finished after resolve  [object Object] 
2025-08-09 09:24:25.094 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:24:34.358 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:24:34.370 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:24:34.372 [info] index finished after resolve  [object Object] 
2025-08-09 09:24:34.372 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:24:49.360 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:24:49.639 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:24:49.642 [info] index finished after resolve  [object Object] 
2025-08-09 09:24:49.642 [info] refresh page data from resolve listeners 0 1076   
2025-08-09 09:27:31.629 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-27-31.webp  [object Object] 
2025-08-09 09:27:31.632 [info] refresh page data from created listeners 0 1077   
2025-08-09 09:27:32.663 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:27:32.837 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:27:32.840 [info] index finished after resolve  [object Object] 
2025-08-09 09:27:32.840 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:27:36.661 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:27:36.671 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:27:36.675 [info] index finished after resolve  [object Object] 
2025-08-09 09:27:36.675 [info] refresh page data from resolve listeners 0 1077   
2025-08-09 09:28:19.051 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-28-19.gif  [object Object] 
2025-08-09 09:28:19.052 [info] refresh page data from created listeners 0 1078   
2025-08-09 09:28:21.094 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:28:21.105 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:28:21.109 [info] index finished after resolve  [object Object] 
2025-08-09 09:28:21.109 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:28:26.357 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:28:26.385 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:28:26.386 [info] index finished after resolve  [object Object] 
2025-08-09 09:28:26.387 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:28:30.751 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-08-09 09:28:30.938 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-08-09 09:28:30.939 [info] index finished after resolve  [object Object] 
2025-08-09 09:28:30.939 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:37:38.128 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:37:38.140 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:37:38.142 [info] index finished after resolve  [object Object] 
2025-08-09 09:37:38.142 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:37:42.642 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:37:42.649 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:37:42.650 [info] index finished after resolve  [object Object] 
2025-08-09 09:37:42.651 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:37:58.967 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:37:58.980 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:37:58.981 [info] index finished after resolve  [object Object] 
2025-08-09 09:37:58.981 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:04.691 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:04.702 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:04.704 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:04.704 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:11.845 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:11.855 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:11.857 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:11.857 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:14.015 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:14.025 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:14.026 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:14.026 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:16.628 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:16.639 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:16.640 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:16.640 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:34.810 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:34.819 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:34.821 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:34.821 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:37.609 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:37.624 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:37.634 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:37.634 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:41.435 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:41.442 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:41.447 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:41.448 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:43.480 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:43.493 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:43.495 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:43.495 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:47.354 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:47.363 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:47.364 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:47.364 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:49.783 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:49.794 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:49.795 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:49.795 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:52.758 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:52.768 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:52.769 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:52.770 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:54.885 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:54.896 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:54.898 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:54.898 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:56.980 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:57.002 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:57.004 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:57.004 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:38:59.005 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:38:59.015 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:38:59.016 [info] index finished after resolve  [object Object] 
2025-08-09 09:38:59.016 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:01.151 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:01.162 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:01.163 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:01.163 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:05.651 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:05.685 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:05.686 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:05.686 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:09.068 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:09.073 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:09.074 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:09.074 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:13.156 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:13.166 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:13.168 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:13.168 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:17.164 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:17.174 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:17.175 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:17.176 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:19.982 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:19.993 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:19.995 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:19.995 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:23.140 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:23.153 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:23.155 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:23.155 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:25.280 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:25.291 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:25.292 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:25.292 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:29.671 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:29.705 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:29.710 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:29.710 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:31.693 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:31.703 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:31.704 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:31.704 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:34.409 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:34.420 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:34.421 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:34.421 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:41.977 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:41.986 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:41.990 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:41.990 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:47.137 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:47.146 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:47.147 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:47.147 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:39:51.453 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:39:51.461 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:39:51.462 [info] index finished after resolve  [object Object] 
2025-08-09 09:39:51.462 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:03.050 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:03.059 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:03.060 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:03.060 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:05.899 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:05.912 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:05.913 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:05.913 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:09.675 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:09.684 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:09.685 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:09.686 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:11.843 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:11.853 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:11.854 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:11.855 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:20.677 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:20.685 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:20.686 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:20.687 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:32.420 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:32.431 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:32.432 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:32.432 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:35.847 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:35.862 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:35.863 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:35.863 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:40.495 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:40.504 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:40.504 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:40.505 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:44.008 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:44.013 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:44.013 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:44.013 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:40:46.069 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:40:46.078 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:40:46.079 [info] index finished after resolve  [object Object] 
2025-08-09 09:40:46.079 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:41:39.033 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:41:39.044 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:41:39.057 [info] index finished after resolve  [object Object] 
2025-08-09 09:41:39.058 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:42:56.984 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:42:57.160 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:42:57.167 [info] index finished after resolve  [object Object] 
2025-08-09 09:42:57.167 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:43:14.434 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:43:14.675 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:43:14.681 [info] index finished after resolve  [object Object] 
2025-08-09 09:43:14.681 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:43:16.234 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:43:16.304 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:43:16.305 [info] index finished after resolve  [object Object] 
2025-08-09 09:43:16.305 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:43:25.930 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:43:26.104 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:43:26.106 [info] index finished after resolve  [object Object] 
2025-08-09 09:43:26.106 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:43:30.353 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:43:30.364 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:43:30.365 [info] index finished after resolve  [object Object] 
2025-08-09 09:43:30.366 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:43:59.670 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:43:59.766 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:43:59.770 [info] index finished after resolve  [object Object] 
2025-08-09 09:43:59.770 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:45:00.831 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:45:00.842 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:45:00.843 [info] index finished after resolve  [object Object] 
2025-08-09 09:45:00.843 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:45:04.160 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:45:04.164 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:45:04.164 [info] index finished after resolve  [object Object] 
2025-08-09 09:45:04.165 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:45:08.992 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 09:45:09.004 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 09:45:09.011 [info] index finished after resolve  [object Object] 
2025-08-09 09:45:09.012 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:28.192 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:28.226 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:28.229 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:28.230 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:31.083 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:31.118 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:31.120 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:31.120 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:33.984 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:33.994 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:33.995 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:33.995 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:36.137 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:36.170 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:36.171 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:36.172 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:38.193 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:38.206 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:38.206 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:38.207 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:40.262 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:40.271 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:40.272 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:40.272 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:42.827 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:42.839 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:42.840 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:42.840 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:46.812 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:46.847 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:46.848 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:46.848 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:51.338 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:51.366 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:51.367 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:51.368 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:46:52.847 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:46:52.859 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:46:52.859 [info] index finished after resolve  [object Object] 
2025-08-09 09:46:52.860 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:47:00.664 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:47:00.694 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:47:00.696 [info] index finished after resolve  [object Object] 
2025-08-09 09:47:00.696 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:47:02.591 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:47:02.643 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:47:02.644 [info] index finished after resolve  [object Object] 
2025-08-09 09:47:02.645 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:47:06.625 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:47:06.660 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:47:06.661 [info] index finished after resolve  [object Object] 
2025-08-09 09:47:06.661 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:47:10.887 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:47:10.922 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:47:10.924 [info] index finished after resolve  [object Object] 
2025-08-09 09:47:10.924 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:47:43.520 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:47:43.530 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:47:43.531 [info] index finished after resolve  [object Object] 
2025-08-09 09:47:43.532 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:03.475 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:03.505 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:03.506 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:03.506 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:05.534 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:05.544 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:05.546 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:05.546 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:07.725 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:07.736 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:07.736 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:07.737 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:09.784 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:09.788 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:09.789 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:09.789 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:11.820 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:11.835 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:11.836 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:11.836 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:16.047 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:16.060 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:16.061 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:16.062 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:18.233 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:18.244 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:18.245 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:18.245 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:21.521 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:21.533 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:21.534 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:21.535 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:23.559 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:23.569 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:23.570 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:23.570 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:26.668 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:26.678 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:26.679 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:26.679 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:29.344 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:29.356 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:29.356 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:29.356 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:31.738 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:31.790 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:31.791 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:31.791 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:35.173 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:35.212 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:35.213 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:35.213 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:37.846 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:37.850 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:37.851 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:37.851 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:42.082 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:42.095 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:42.096 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:42.096 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:46.072 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:46.108 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:46.108 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:46.109 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:49.466 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:49.479 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:49.480 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:49.480 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:51.952 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:51.965 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:51.966 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:51.966 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:54.188 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:54.221 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:54.223 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:54.224 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:55.773 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:55.824 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:55.825 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:55.825 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:48:59.326 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:48:59.352 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:48:59.354 [info] index finished after resolve  [object Object] 
2025-08-09 09:48:59.354 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:49:01.420 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:49:01.450 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:49:01.452 [info] index finished after resolve  [object Object] 
2025-08-09 09:49:01.453 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:49:08.303 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:49:08.312 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:49:08.313 [info] index finished after resolve  [object Object] 
2025-08-09 09:49:08.313 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:49:10.938 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:49:10.943 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:49:10.943 [info] index finished after resolve  [object Object] 
2025-08-09 09:49:10.944 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:49:16.742 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:49:16.769 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:49:16.771 [info] index finished after resolve  [object Object] 
2025-08-09 09:49:16.771 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:49:23.689 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:49:23.698 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:49:23.699 [info] index finished after resolve  [object Object] 
2025-08-09 09:49:23.699 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:50:26.593 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:50:26.606 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:50:26.608 [info] index finished after resolve  [object Object] 
2025-08-09 09:50:26.608 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:50:48.254 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:50:48.262 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:50:48.263 [info] index finished after resolve  [object Object] 
2025-08-09 09:50:48.264 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:55:41.561 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:55:41.573 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:55:41.574 [info] index finished after resolve  [object Object] 
2025-08-09 09:55:41.575 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:55:43.859 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:55:43.889 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:55:43.891 [info] index finished after resolve  [object Object] 
2025-08-09 09:55:43.892 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:55:45.951 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:55:45.979 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:55:45.981 [info] index finished after resolve  [object Object] 
2025-08-09 09:55:45.981 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:55:48.013 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:55:48.025 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:55:48.045 [info] index finished after resolve  [object Object] 
2025-08-09 09:55:48.045 [info] refresh page data from resolve listeners 0 1078   
2025-08-09 09:58:42.970 [info] indexing created file 学习库/Deep learning/概念库/attachments/独热编码(One-Hot Encoding)-2025-08-09,09-08,09-58-42.png  [object Object] 
2025-08-09 09:58:42.972 [info] refresh page data from created listeners 0 1079   
2025-08-09 09:58:45.010 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:58:45.025 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:58:45.026 [info] index finished after resolve  [object Object] 
2025-08-09 09:58:45.026 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:58:50.705 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:58:50.709 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:58:50.710 [info] index finished after resolve  [object Object] 
2025-08-09 09:58:50.710 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:58:59.890 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:58:59.901 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:58:59.902 [info] index finished after resolve  [object Object] 
2025-08-09 09:58:59.902 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:03.639 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:03.652 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:03.653 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:03.654 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:15.511 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:15.522 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:15.523 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:15.523 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:17.615 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:17.624 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:17.624 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:17.625 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:20.134 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:20.144 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:20.144 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:20.145 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:22.723 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:22.743 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:22.744 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:22.744 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:24.739 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:24.751 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:24.751 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:24.752 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:27.023 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:27.034 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:27.035 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:27.035 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:29.078 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:29.099 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:29.100 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:29.101 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:31.089 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:31.093 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:31.094 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:31.094 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:33.306 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:33.319 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:33.320 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:33.320 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:37.821 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:37.831 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:37.832 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:37.832 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:39.971 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:39.983 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:39.983 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:39.984 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:42.057 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:42.080 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:42.080 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:42.081 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:44.080 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:44.101 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:44.102 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:44.103 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 09:59:46.103 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 09:59:46.112 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 09:59:46.113 [info] index finished after resolve  [object Object] 
2025-08-09 09:59:46.113 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 10:01:30.697 [debug] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-08-09 10:01:30.871 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-08-09 10:01:30.878 [info] index finished after resolve  [object Object] 
2025-08-09 10:01:30.879 [info] refresh page data from resolve listeners 0 1079   
2025-08-09 10:01:45.342 [info] indexing created file 学习库/Deep learning/概念库/未命名.md  [object Object] 
2025-08-09 10:01:45.342 [info] indexing created ignore file 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:01:45.417 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-09 10:01:45.451 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:01:45.455 [info] index finished after resolve  [object Object] 
2025-08-09 10:01:45.455 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:01:49.372 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:01:49.377 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:01:49.378 [info] index finished after resolve  [object Object] 
2025-08-09 10:01:49.378 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:01:55.747 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:01:55.759 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:01:55.760 [info] index finished after resolve  [object Object] 
2025-08-09 10:01:55.760 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:01:57.897 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:01:57.908 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:01:57.909 [info] index finished after resolve  [object Object] 
2025-08-09 10:01:57.910 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:02:04.571 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:02:04.594 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:02:04.596 [info] index finished after resolve  [object Object] 
2025-08-09 10:02:04.596 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:02:06.766 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:02:06.791 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:02:06.791 [info] index finished after resolve  [object Object] 
2025-08-09 10:02:06.791 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:02:09.391 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:02:09.418 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:02:09.419 [info] index finished after resolve  [object Object] 
2025-08-09 10:02:09.419 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:02:11.705 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:02:11.715 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:02:11.716 [info] index finished after resolve  [object Object] 
2025-08-09 10:02:11.716 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:02:13.740 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:02:13.762 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:02:13.765 [info] index finished after resolve  [object Object] 
2025-08-09 10:02:13.765 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:02:18.713 [debug] ignore file modify evnet 学习库/Deep learning/概念库/未命名.md   
2025-08-09 10:02:18.724 [info] trigger 学习库/Deep learning/概念库/未命名.md resolve  [object Object] 
2025-08-09 10:02:18.724 [info] index finished after resolve  [object Object] 
2025-08-09 10:02:18.725 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:02:25.343 [info] refresh page data from rename listeners 0 1080   
2025-08-09 10:02:25.348 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-09 10:02:41.849 [info] refresh page data from rename listeners 0 1080   
2025-08-09 10:02:50.382 [info] refresh page data from rename listeners 0 1080   
2025-08-09 10:02:50.386 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-08-09 10:02:52.597 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-08-09 10:02:52.622 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-08-09 10:02:52.624 [info] index finished after resolve  [object Object] 
2025-08-09 10:02:52.624 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:16.570 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:16.580 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:16.581 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:16.581 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:18.645 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:18.657 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:18.658 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:18.659 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:20.715 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:20.726 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:20.727 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:20.728 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:22.882 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:22.894 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:22.895 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:22.896 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:26.095 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:26.106 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:26.107 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:26.107 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:28.315 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:28.317 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:28.323 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:28.324 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:34.317 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:34.328 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:34.329 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:34.329 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:36.457 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:36.469 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:36.471 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:36.471 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:40.172 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:40.181 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:40.181 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:40.182 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:44.795 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:44.807 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:44.808 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:44.808 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:47.048 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:47.059 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:47.061 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:47.061 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:49.212 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:49.223 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:49.224 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:49.224 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:03:59.477 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:03:59.487 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:03:59.488 [info] index finished after resolve  [object Object] 
2025-08-09 10:03:59.489 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:04.203 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:04.215 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:04.216 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:04.216 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:06.686 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:06.697 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:06.698 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:06.699 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:09.140 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:09.144 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:09.150 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:09.150 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:11.593 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:11.612 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:11.613 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:11.613 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:13.627 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:13.637 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:13.638 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:13.638 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:15.850 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:15.860 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:15.861 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:15.862 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:18.393 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:18.402 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:18.403 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:18.403 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:20.431 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:20.441 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:20.443 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:20.443 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:23.042 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:23.051 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:23.051 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:23.051 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:04:25.536 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:04:25.547 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:04:25.547 [info] index finished after resolve  [object Object] 
2025-08-09 10:04:25.548 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:08:40.469 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:08:40.481 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:08:40.482 [info] index finished after resolve  [object Object] 
2025-08-09 10:08:40.483 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:09:00.717 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:09:00.728 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:09:00.729 [info] index finished after resolve  [object Object] 
2025-08-09 10:09:00.729 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:09:03.502 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:09:03.506 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:09:03.513 [info] index finished after resolve  [object Object] 
2025-08-09 10:09:03.514 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:09:17.584 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:09:17.594 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:09:17.597 [info] index finished after resolve  [object Object] 
2025-08-09 10:09:17.597 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:09:28.889 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:09:28.900 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:09:28.900 [info] index finished after resolve  [object Object] 
2025-08-09 10:09:28.901 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:09:37.873 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:09:37.884 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:09:37.884 [info] index finished after resolve  [object Object] 
2025-08-09 10:09:37.885 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:09:40.248 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:09:40.259 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:09:40.260 [info] index finished after resolve  [object Object] 
2025-08-09 10:09:40.261 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:05.339 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:05.350 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:05.351 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:05.352 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:07.467 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:07.476 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:07.477 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:07.477 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:09.755 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:09.766 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:09.768 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:09.768 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:11.933 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:11.943 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:11.944 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:11.944 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:13.988 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:13.999 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:14.000 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:14.000 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:16.090 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:16.096 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:16.103 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:16.103 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:18.198 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:18.206 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:18.207 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:18.208 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:20.472 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:20.486 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:20.487 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:20.488 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:22.963 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:22.972 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:22.972 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:22.973 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:43.575 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:43.585 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:43.586 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:43.587 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:45.840 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:45.851 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:45.851 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:45.852 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:47.888 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:47.898 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:47.899 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:47.899 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:10:53.887 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:10:53.898 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:10:53.898 [info] index finished after resolve  [object Object] 
2025-08-09 10:10:53.899 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:11:00.179 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:11:00.189 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:11:00.189 [info] index finished after resolve  [object Object] 
2025-08-09 10:11:00.189 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:11:02.284 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:11:02.294 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:11:02.295 [info] index finished after resolve  [object Object] 
2025-08-09 10:11:02.295 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:11:42.124 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:11:42.127 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:11:42.128 [info] index finished after resolve  [object Object] 
2025-08-09 10:11:42.128 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:11:52.761 [info] indexing created file 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md  [object Object] 
2025-08-09 10:11:52.761 [info] indexing created ignore file 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:11:52.967 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:11:52.979 [info] index finished after resolve  [object Object] 
2025-08-09 10:11:52.980 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:12:04.681 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 10:12:05.043 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-09 10:12:05.048 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-09 10:12:05.052 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-09 10:12:05.062 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-09 10:12:26.375 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:12:26.389 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:12:26.391 [info] index finished after resolve  [object Object] 
2025-08-09 10:12:26.391 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:12:42.305 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:12:42.319 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:12:42.321 [info] index finished after resolve  [object Object] 
2025-08-09 10:12:42.322 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:13:03.023 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:13:03.034 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:13:03.035 [info] index finished after resolve  [object Object] 
2025-08-09 10:13:03.036 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:13:16.191 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:13:16.204 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:13:16.206 [info] index finished after resolve  [object Object] 
2025-08-09 10:13:16.206 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:13:31.925 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:13:31.943 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:13:31.944 [info] index finished after resolve  [object Object] 
2025-08-09 10:13:31.944 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:13:50.973 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:13:50.987 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:13:50.989 [info] index finished after resolve  [object Object] 
2025-08-09 10:13:50.989 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:14:06.709 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:14:06.719 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:14:06.721 [info] index finished after resolve  [object Object] 
2025-08-09 10:14:06.721 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:14:16.506 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:14:16.516 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:14:16.520 [info] index finished after resolve  [object Object] 
2025-08-09 10:14:16.520 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:14:24.279 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:14:24.287 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:14:24.288 [info] index finished after resolve  [object Object] 
2025-08-09 10:14:24.288 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:14:33.493 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:14:33.505 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:14:33.506 [info] index finished after resolve  [object Object] 
2025-08-09 10:14:33.507 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:14:52.061 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:14:52.075 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:14:52.076 [info] index finished after resolve  [object Object] 
2025-08-09 10:14:52.076 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:15:20.374 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:15:20.384 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:15:20.386 [info] index finished after resolve  [object Object] 
2025-08-09 10:15:20.387 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:15:49.963 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:15:49.976 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:15:49.978 [info] index finished after resolve  [object Object] 
2025-08-09 10:15:49.978 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:16:05.266 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:16:05.281 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:16:05.283 [info] index finished after resolve  [object Object] 
2025-08-09 10:16:05.283 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:16:08.184 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:16:08.197 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:16:08.199 [info] index finished after resolve  [object Object] 
2025-08-09 10:16:08.199 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:16:23.877 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:16:23.889 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:16:23.891 [info] index finished after resolve  [object Object] 
2025-08-09 10:16:23.891 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:16:25.147 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:16:25.161 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:16:25.163 [info] index finished after resolve  [object Object] 
2025-08-09 10:16:25.163 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:16:32.736 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:16:32.752 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:16:32.755 [info] index finished after resolve  [object Object] 
2025-08-09 10:16:32.756 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:16:48.468 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:16:48.479 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:16:48.481 [info] index finished after resolve  [object Object] 
2025-08-09 10:16:48.482 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:17:12.749 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:17:12.894 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:17:12.895 [info] index finished after resolve  [object Object] 
2025-08-09 10:17:12.895 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:17:14.744 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:17:14.759 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:17:14.760 [info] index finished after resolve  [object Object] 
2025-08-09 10:17:14.761 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:17:24.713 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:17:24.726 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:17:24.728 [info] index finished after resolve  [object Object] 
2025-08-09 10:17:24.729 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:17:34.035 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:17:34.050 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:17:34.051 [info] index finished after resolve  [object Object] 
2025-08-09 10:17:34.051 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:17:52.697 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:17:52.710 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:17:52.712 [info] index finished after resolve  [object Object] 
2025-08-09 10:17:52.712 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:18:03.225 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:18:03.235 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:18:03.236 [info] index finished after resolve  [object Object] 
2025-08-09 10:18:03.236 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:18:20.402 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:18:20.418 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:18:20.420 [info] index finished after resolve  [object Object] 
2025-08-09 10:18:20.421 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:18:22.214 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:18:22.229 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:18:22.231 [info] index finished after resolve  [object Object] 
2025-08-09 10:18:22.231 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:18:36.288 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:18:36.370 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:18:36.371 [info] index finished after resolve  [object Object] 
2025-08-09 10:18:36.372 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:18:53.797 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:18:53.800 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:18:53.801 [info] index finished after resolve  [object Object] 
2025-08-09 10:18:53.801 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:19:11.878 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:19:11.897 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:19:11.905 [info] index finished after resolve  [object Object] 
2025-08-09 10:19:11.906 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:19:18.633 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:19:18.644 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:19:18.646 [info] index finished after resolve  [object Object] 
2025-08-09 10:19:18.646 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:19:28.921 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:19:28.936 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:19:28.937 [info] index finished after resolve  [object Object] 
2025-08-09 10:19:28.937 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:19:52.646 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:19:52.657 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:19:52.661 [info] index finished after resolve  [object Object] 
2025-08-09 10:19:52.661 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:19:55.383 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:19:55.432 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:19:55.434 [info] index finished after resolve  [object Object] 
2025-08-09 10:19:55.434 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:20:00.052 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:20:00.063 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:20:00.064 [info] index finished after resolve  [object Object] 
2025-08-09 10:20:00.065 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:20:03.629 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 10:20:03.999 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-09 10:20:04.003 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-09 10:20:04.007 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-09 10:20:04.012 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-09 10:20:34.687 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:20:34.703 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:20:34.704 [info] index finished after resolve  [object Object] 
2025-08-09 10:20:34.704 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:20:46.583 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:20:46.598 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:20:46.599 [info] index finished after resolve  [object Object] 
2025-08-09 10:20:46.600 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:20:59.405 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:20:59.418 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:20:59.419 [info] index finished after resolve  [object Object] 
2025-08-09 10:20:59.420 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:07.669 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:21:07.674 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:21:07.684 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:07.685 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:22.310 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:21:22.322 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:21:22.324 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:22.324 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:32.115 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:21:32.128 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:21:32.129 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:32.129 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:37.284 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:21:37.295 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:21:37.296 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:37.296 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:41.918 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:21:41.929 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:21:41.930 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:41.930 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:44.459 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:21:44.474 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:21:44.474 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:44.475 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:47.102 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:21:47.136 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:21:47.138 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:47.138 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:21:52.066 [info] components database created cost 1 ms   
2025-08-09 10:21:52.066 [info] components index initializing...   
2025-08-09 10:21:52.192 [info] start to batch put pages: 5   
2025-08-09 10:21:52.199 [info] batch persist cost 5  7 
2025-08-09 10:21:52.237 [info] components index initialized, 1081 files cost 172 ms   
2025-08-09 10:21:52.238 [info] refresh page data from init listeners 0 1081   
2025-08-09 10:21:55.275 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:21:55.308 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:21:55.311 [info] index finished after resolve  [object Object] 
2025-08-09 10:21:55.313 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:07.049 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:07.053 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:07.054 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:07.054 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:09.256 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:09.262 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:09.264 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:09.265 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:14.246 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:14.250 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:14.251 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:14.251 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:16.382 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:16.386 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:16.387 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:16.387 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:18.410 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:18.415 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:18.415 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:18.416 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:20.491 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:20.496 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:20.496 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:20.497 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:25.137 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 10:22:25.283 [debug] ignore file modify evnet 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md   
2025-08-09 10:22:25.304 [info] trigger 学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md resolve  [object Object] 
2025-08-09 10:22:25.306 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:25.306 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:29.201 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:29.206 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:29.207 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:29.207 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:22:34.484 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:22:34.488 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:22:34.489 [info] index finished after resolve  [object Object] 
2025-08-09 10:22:34.489 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:05.246 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:05.252 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:05.253 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:05.253 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:17.997 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:18.018 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:18.019 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:18.020 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:30.631 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:30.635 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:30.636 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:30.637 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:37.941 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:37.949 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:37.950 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:37.950 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:40.573 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:40.577 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:40.578 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:40.578 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:43.382 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:43.389 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:43.390 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:43.390 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:45.508 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:45.515 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:45.516 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:45.516 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:47.750 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:47.756 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:47.757 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:47.758 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:49.883 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:49.888 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:49.889 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:49.889 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:51.969 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:51.976 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:51.976 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:51.977 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:23:54.732 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:23:54.740 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:23:54.741 [info] index finished after resolve  [object Object] 
2025-08-09 10:23:54.742 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:21.642 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:21.649 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:21.650 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:21.650 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:24.916 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:24.923 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:24.923 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:24.924 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:27.641 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:27.649 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:27.650 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:27.650 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:29.917 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:29.921 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:29.922 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:29.922 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:36.405 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:36.412 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:36.412 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:36.413 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:49.052 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:49.058 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:49.059 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:49.059 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:54.378 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:54.383 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:54.383 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:54.384 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:24:59.773 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:24:59.780 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:24:59.782 [info] index finished after resolve  [object Object] 
2025-08-09 10:24:59.782 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:25:05.744 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:25:05.751 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:25:05.751 [info] index finished after resolve  [object Object] 
2025-08-09 10:25:05.752 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:25:08.048 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:25:08.054 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:25:08.055 [info] index finished after resolve  [object Object] 
2025-08-09 10:25:08.055 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:25:11.735 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:25:11.740 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:25:11.740 [info] index finished after resolve  [object Object] 
2025-08-09 10:25:11.741 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:25:18.166 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:25:18.173 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:25:18.174 [info] index finished after resolve  [object Object] 
2025-08-09 10:25:18.174 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:25:25.988 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:25:25.993 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:25:25.994 [info] index finished after resolve  [object Object] 
2025-08-09 10:25:25.994 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:25:54.658 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:25:54.664 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:25:54.667 [info] index finished after resolve  [object Object] 
2025-08-09 10:25:54.668 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:15.106 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:15.112 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:15.129 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:15.129 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:20.373 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:20.380 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:20.381 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:20.381 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:23.051 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:23.075 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:23.076 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:23.076 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:29.323 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:29.328 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:29.330 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:29.330 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:32.369 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:32.375 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:32.375 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:32.376 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:34.652 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:34.657 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:34.658 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:34.658 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:40.876 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:40.884 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:40.885 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:40.885 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:26:44.523 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:26:44.544 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:26:44.545 [info] index finished after resolve  [object Object] 
2025-08-09 10:26:44.545 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:27:35.859 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:27:35.864 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:27:35.866 [info] index finished after resolve  [object Object] 
2025-08-09 10:27:35.866 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:27:42.459 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:27:42.463 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:27:42.464 [info] index finished after resolve  [object Object] 
2025-08-09 10:27:42.464 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:28:32.970 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:28:32.975 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:28:32.977 [info] index finished after resolve  [object Object] 
2025-08-09 10:28:32.977 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:28:35.116 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:28:35.141 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:28:35.142 [info] index finished after resolve  [object Object] 
2025-08-09 10:28:35.142 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:28:42.681 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:28:42.687 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:28:42.688 [info] index finished after resolve  [object Object] 
2025-08-09 10:28:42.688 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:28:44.794 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:28:44.799 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:28:44.801 [info] index finished after resolve  [object Object] 
2025-08-09 10:28:44.801 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:28:56.638 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:28:56.659 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:28:56.660 [info] index finished after resolve  [object Object] 
2025-08-09 10:28:56.660 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:29:06.965 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:29:06.973 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:29:06.974 [info] index finished after resolve  [object Object] 
2025-08-09 10:29:06.974 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:29:09.211 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:29:09.215 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:29:09.216 [info] index finished after resolve  [object Object] 
2025-08-09 10:29:09.217 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:21.903 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:21.910 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:21.911 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:21.911 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:24.025 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:24.031 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:24.031 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:24.032 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:26.125 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:26.130 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:26.132 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:26.132 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:30.879 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:30.885 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:30.886 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:30.886 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:33.014 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:33.021 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:33.022 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:33.022 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:35.050 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:35.058 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:35.059 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:35.059 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:37.381 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:37.388 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:37.390 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:37.390 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:39.630 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:39.645 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:39.648 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:39.648 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:41.646 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:41.652 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:41.653 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:41.653 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:43.703 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:43.711 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:43.712 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:43.713 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:45.991 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:45.997 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:45.998 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:45.998 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:48.577 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:48.584 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:48.584 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:48.585 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:52.049 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:52.053 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:52.054 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:52.054 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:54.450 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:54.457 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:54.458 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:54.458 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:30:56.500 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:30:56.505 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:30:56.506 [info] index finished after resolve  [object Object] 
2025-08-09 10:30:56.506 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:00.683 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:00.691 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:00.692 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:00.692 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:03.082 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:03.088 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:03.091 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:03.091 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:05.250 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:05.258 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:05.258 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:05.259 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:12.997 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:13.003 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:13.004 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:13.004 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:15.082 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:15.088 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:15.090 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:15.090 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:18.551 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:18.558 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:18.559 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:18.559 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:20.593 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:20.598 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:20.598 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:20.599 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:22.685 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:22.700 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:22.700 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:22.701 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:24.701 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:24.710 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:24.710 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:24.711 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:26.947 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:26.953 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:26.954 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:26.954 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:30.417 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:30.424 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:30.425 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:30.425 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:31:32.453 [debug] ignore file modify evnet 学习库/Deep learning/概念库/词嵌入（word embedding）.md   
2025-08-09 10:31:32.461 [info] trigger 学习库/Deep learning/概念库/词嵌入（word embedding）.md resolve  [object Object] 
2025-08-09 10:31:32.462 [info] index finished after resolve  [object Object] 
2025-08-09 10:31:32.462 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:36:52.566 [info] components database created cost 1 ms   
2025-08-09 10:36:52.567 [info] components index initializing...   
2025-08-09 10:36:52.799 [info] start to batch put pages: 1   
2025-08-09 10:36:52.815 [info] batch persist cost 1  16 
2025-08-09 10:36:52.858 [info] components index initialized, 1081 files cost 293 ms   
2025-08-09 10:36:52.858 [info] refresh page data from init listeners 0 1081   
2025-08-09 10:36:54.282 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 10:36:54.603 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-09 10:36:55.095 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-09 10:36:55.110 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-09 10:36:55.121 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-09 10:36:55.125 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-09 10:37:36.531 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:37:36.545 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:37:36.547 [info] index finished after resolve  [object Object] 
2025-08-09 10:37:36.548 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:37:38.640 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:37:38.646 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:37:38.649 [info] index finished after resolve  [object Object] 
2025-08-09 10:37:38.649 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:37:40.979 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:37:40.985 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:37:40.986 [info] index finished after resolve  [object Object] 
2025-08-09 10:37:40.986 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:37:42.936 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:37:42.996 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:37:42.997 [info] index finished after resolve  [object Object] 
2025-08-09 10:37:42.998 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:37:50.169 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:37:50.235 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:37:50.242 [info] index finished after resolve  [object Object] 
2025-08-09 10:37:50.242 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:38:03.032 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:38:03.037 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:38:03.038 [info] index finished after resolve  [object Object] 
2025-08-09 10:38:03.038 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:38:20.106 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:38:20.131 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:38:20.134 [info] index finished after resolve  [object Object] 
2025-08-09 10:38:20.134 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:38:23.906 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:38:23.911 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:38:23.912 [info] index finished after resolve  [object Object] 
2025-08-09 10:38:23.912 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:38:26.517 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:38:26.523 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:38:26.525 [info] index finished after resolve  [object Object] 
2025-08-09 10:38:26.525 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:38:29.503 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:38:29.508 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:38:29.509 [info] index finished after resolve  [object Object] 
2025-08-09 10:38:29.510 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:38:32.436 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/基本概念.md   
2025-08-09 10:38:32.443 [info] trigger 学习库/Anki/Artificial Intelligence/基本概念.md resolve  [object Object] 
2025-08-09 10:38:32.443 [info] index finished after resolve  [object Object] 
2025-08-09 10:38:32.444 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 10:45:04.567 [info] refresh page data from delete listeners 0 1080   
2025-08-09 10:45:46.516 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Transformer.md   
2025-08-09 10:45:46.522 [info] trigger 学习库/Deep learning/概念库/Transformer.md resolve  [object Object] 
2025-08-09 10:45:46.523 [info] index finished after resolve  [object Object] 
2025-08-09 10:45:46.523 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:45:49.024 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Transformer.md   
2025-08-09 10:45:49.029 [info] trigger 学习库/Deep learning/概念库/Transformer.md resolve  [object Object] 
2025-08-09 10:45:49.029 [info] index finished after resolve  [object Object] 
2025-08-09 10:45:49.030 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:45:51.230 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Transformer.md   
2025-08-09 10:45:51.234 [info] trigger 学习库/Deep learning/概念库/Transformer.md resolve  [object Object] 
2025-08-09 10:45:51.235 [info] index finished after resolve  [object Object] 
2025-08-09 10:45:51.235 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:45:53.768 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Transformer.md   
2025-08-09 10:45:53.774 [info] trigger 学习库/Deep learning/概念库/Transformer.md resolve  [object Object] 
2025-08-09 10:45:53.776 [info] index finished after resolve  [object Object] 
2025-08-09 10:45:53.776 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 10:45:57.606 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Transformer.md   
2025-08-09 10:45:57.612 [info] trigger 学习库/Deep learning/概念库/Transformer.md resolve  [object Object] 
2025-08-09 10:45:57.613 [info] index finished after resolve  [object Object] 
2025-08-09 10:45:57.613 [info] refresh page data from resolve listeners 0 1080   
2025-08-09 11:28:40.883 [info] indexing created file 学习库/Artificial Intelligence/未命名.md  [object Object] 
2025-08-09 11:28:40.883 [info] indexing created ignore file 学习库/Artificial Intelligence/未命名.md   
2025-08-09 11:28:40.895 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-09 11:28:40.990 [info] trigger 学习库/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-09 11:28:41.035 [info] index finished after resolve  [object Object] 
2025-08-09 11:28:41.036 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:28:44.694 [info] refresh page data from rename listeners 0 1081   
2025-08-09 11:28:44.698 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-09 11:28:49.124 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:28:49.131 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:28:49.133 [info] index finished after resolve  [object Object] 
2025-08-09 11:28:49.133 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:28:54.376 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:28:54.385 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:28:54.385 [info] index finished after resolve  [object Object] 
2025-08-09 11:28:54.386 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:28:59.109 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:28:59.116 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:28:59.117 [info] index finished after resolve  [object Object] 
2025-08-09 11:28:59.118 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:29:01.684 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:29:01.688 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:29:01.689 [info] index finished after resolve  [object Object] 
2025-08-09 11:29:01.689 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:29:17.752 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:29:17.757 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:29:17.758 [info] index finished after resolve  [object Object] 
2025-08-09 11:29:17.758 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:29:41.043 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:29:41.062 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:29:41.064 [info] index finished after resolve  [object Object] 
2025-08-09 11:29:41.064 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:29:43.935 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:29:43.940 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:29:43.941 [info] index finished after resolve  [object Object] 
2025-08-09 11:29:43.941 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:29:46.345 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:29:46.352 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:29:46.352 [info] index finished after resolve  [object Object] 
2025-08-09 11:29:46.353 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:29:49.018 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:29:49.023 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:29:49.024 [info] index finished after resolve  [object Object] 
2025-08-09 11:29:49.024 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:01.857 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:01.862 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:01.863 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:01.863 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:10.785 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:10.791 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:10.792 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:10.792 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:22.507 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:22.512 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:22.513 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:22.513 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:28.323 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:28.329 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:28.330 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:28.330 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:30.364 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:30.370 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:30.371 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:30.371 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:32.702 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:32.708 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:32.708 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:32.708 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:35.377 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:35.398 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:35.400 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:35.400 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:37.417 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:37.422 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:37.423 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:37.423 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:30:39.592 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:30:39.599 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:30:39.619 [info] index finished after resolve  [object Object] 
2025-08-09 11:30:39.620 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:01.839 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:01.845 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:01.846 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:01.846 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:04.487 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:04.493 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:04.494 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:04.495 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:30.389 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:30.394 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:30.395 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:30.395 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:32.382 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:32.389 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:32.390 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:32.390 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:34.561 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:34.566 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:34.569 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:34.569 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:36.621 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:36.625 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:36.626 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:36.626 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:38.844 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:38.851 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:38.852 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:38.852 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:31:54.342 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:31:54.346 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:31:54.347 [info] index finished after resolve  [object Object] 
2025-08-09 11:31:54.347 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:00.338 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:00.344 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:00.345 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:00.346 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:04.222 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:04.227 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:04.228 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:04.229 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:13.344 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:13.350 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:13.350 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:13.351 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:22.550 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:22.556 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:22.557 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:22.557 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:28.157 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:28.162 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:28.163 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:28.163 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:36.737 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:36.742 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:36.744 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:36.744 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:41.646 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:41.652 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:41.654 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:41.654 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:43.809 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:43.813 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:43.814 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:43.815 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:50.075 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:50.081 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:50.081 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:50.082 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:32:59.841 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:32:59.846 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:32:59.846 [info] index finished after resolve  [object Object] 
2025-08-09 11:32:59.846 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:33:19.539 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:33:19.545 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:33:19.546 [info] index finished after resolve  [object Object] 
2025-08-09 11:33:19.547 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 11:33:22.011 [debug] ignore file modify evnet 学习库/Artificial Intelligence/Token.md   
2025-08-09 11:33:22.015 [info] trigger 学习库/Artificial Intelligence/Token.md resolve  [object Object] 
2025-08-09 11:33:22.016 [info] index finished after resolve  [object Object] 
2025-08-09 11:33:22.016 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 15:04:57.661 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-08-09 15:04:57.753 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-08-09 15:04:57.754 [info] index finished after resolve  [object Object] 
2025-08-09 15:04:57.754 [info] refresh page data from resolve listeners 0 1081   
2025-08-09 15:05:03.305 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-08-09 15:05:03.371 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-08-09 15:05:03.374 [info] index finished after resolve  [object Object] 
2025-08-09 15:05:03.374 [info] refresh page data from resolve listeners 0 1081   

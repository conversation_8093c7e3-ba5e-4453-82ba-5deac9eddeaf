{"main": {"id": "9a92b85be99e6512", "type": "split", "children": [{"id": "86f138b7c5e5e225", "type": "tabs", "children": [{"id": "915679417acb6b15", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/Deep learning/概念库/Transformer.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "Transformer"}}, {"id": "6a069c35403b21e9", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/Deep learning/概念库/Attention.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "Attention"}}, {"id": "015bca11bcbaedb9", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/Artificial Intelligence/Token.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "Token"}}, {"id": "41b9a5986b27fdbc", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/Deep learning/pytorch/10. 循环神经网络（Recurrent Neural Network）.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "10. 循环神经网络（Recurrent Neural Network）"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "ccafe5c1edb39392", "type": "split", "children": [{"id": "ff985ebb9e64a67a", "type": "tabs", "children": [{"id": "0471e214e11a0e3e", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "df897e713d50ad26", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "byModifiedTime"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "a54f5d1b66aa9af0", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "ea807e711cd67a77", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "clock", "title": "Recent Files"}}]}], "direction": "horizontal", "width": 301.5}, "right": {"id": "589685af648bfa09", "type": "split", "children": [{"id": "ebf068d98b8f5d7e", "type": "tabs", "dimension": 67.8921568627451, "children": [{"id": "503da8d536964a67", "type": "leaf", "state": {"type": "backlink", "state": {"file": "学习库/python笔记/项目管理.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "项目管理 的反向链接列表"}}, {"id": "4eb73336137b77fc", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "出链"}}, {"id": "dd44a90d63472d6d", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "3c9fff9b1aac3ed1", "type": "leaf", "state": {"type": "copilot-chat-view", "state": {}, "icon": "message-square", "title": "Copilot"}}, {"id": "01747ffb46c2e2db", "type": "leaf", "state": {"type": "comment-view", "state": {}, "icon": "highlighter", "title": "<PERSON><PERSON><PERSON>"}}, {"id": "30bb109630e92a1d", "type": "leaf", "state": {"type": "chinese-calendar-view", "state": {}, "icon": "calendar-with-checkmark", "title": "日历"}}, {"id": "d6fd2d8d71bdc2af", "type": "leaf", "state": {"type": "review-queue-list-view", "state": {}, "icon": "SpacedRepIcon", "title": "笔记复习序列"}}], "currentTab": 6}, {"id": "362ffd7addd9ee9d", "type": "tabs", "dimension": 32.1078431372549, "children": [{"id": "4734f418b64acbcd", "type": "leaf", "state": {"type": "advanced-tables-toolbar", "state": {}, "icon": "spreadsheet", "title": "Advanced Tables"}}, {"id": "6b8468d83e031abe", "type": "leaf", "state": {"type": "outline", "state": {"file": "文献库/算法库/CIOU/算法卡 -CIOU（完全交并比，complete intersection over union）-.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "算法卡 -CIOU（完全交并比，complete intersection over union）- 的大纲"}}]}], "direction": "horizontal", "width": 267.5}, "left-ribbon": {"hiddenItems": {"hi-note:HiNote": false, "pdf-plus:PDF++: Toggle auto-copy": false, "pdf-plus:PDF++: Toggle auto-focus": false, "pdf-plus:PDF++: Toggle auto-paste": false, "copilot:Open Copilot Chat": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "obsidian-excalidraw-plugin:新建绘图文件": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "obsidian-spaced-repetition:复习卡片": false, "obsidian-kanban:创建新看板": false, "omnisearch:Omnisearch": false, "templater-obsidian:Templater": false, "homepage:Open homepage": false}}, "active": "6a069c35403b21e9", "lastOpenFiles": ["学习库/Deep learning/概念库/Transformer.md", "学习库/Deep learning/pytorch/10. 循环神经网络（Recurrent Neural Network）.md", "学习库/Artificial Intelligence/Token.md", "学习库/Deep learning/概念库/Attention.md", "学习库/Deep learning/概念库/concatenate和add.md", "学习库/Deep learning/概念库/Drawing 2025-07-12 09.54.59.excalidraw.md", "学习库/An<PERSON>/Artificial Intelligence/基本概念.md", "学习库/Deep learning/概念库/独热编码（One-Hot Encoding）.md", "Home/Home.md", "学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md", "学习库/obsidian 插件使用说明/Admonition.md", "学习库/Deep learning/概念库/词嵌入（word embedding）.md", "学习库/Latex/Latex 从入门到如土.md", "学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md", "学习库/Deep learning/概念库/excalidraw", "学习库/Deep learning/概念库/标签分配.md", "学习库/Deep learning/概念库/attachments/独热编码(One-Hot Encoding)-2025-08-09,09-08,09-58-42.png", "学习库/Deep learning/概念库/神经网络.md", "学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md", "学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md", "学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-28-19.gif", "学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-27-31.webp", "学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-08-09,09-08,09-03-43.webp", "学习库/Deep learning/概念库/attachments/神经网络-2025-08-09,08-08,08-54-38.png", "学习库/Deep learning/概念库/attachments/神经网络-2025-08-09,08-08,08-48-42.png", "学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md", "学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md", "学习库/Deep learning/概念库/神经网络的调参.md", "学习库/Anki/Artificial Intelligence/调参.md", "学习库/linux/常用Linux命令_优化版.md", "components/logs/2025-08-09.components.log", "学习库/Deep learning/概念库/卷积和转置卷积.md", "学习库/An<PERSON>/Deep learning/概念.md", "学习库/An<PERSON>/Artificial Intelligence/评价指标.md", "学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,22-08,22-01-29.gif", "学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,22-08,22-00-02.webp", "学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,21-08,21-59-11.webp", "学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,21-08,21-58-55.webp", "components/logs/2025-08-08.components.log", "学习库/linux/00-导航.components", "components/logs/2025-08-05.components.log", "att/picture/running.gif.crdownload", "Wiki Home.components", "Home.components", "components/logs/2025-08-04.components.log", "components/logs"]}
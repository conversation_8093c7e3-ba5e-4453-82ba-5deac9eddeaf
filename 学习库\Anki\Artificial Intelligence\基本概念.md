---
tags:
  - anki
  - AI
  - Artifical_Intelligence
  - AI_ANKI
---
## 通过正向卷积操作，输入和输出建立了怎样的关系？
```ad-question
title: 
```
?
```ad-success
collapse: close
![[卷积和转置卷积#^1ef9f2]]
```
<!--SR:!2025-09-15,45,290-->

## 思考下什么是均方误差呢，均方误差的作用是什么呢？
?
```ad-success
title: 答案
collapse: close
 均方误差是损失函数的一种，用于评估所有真实值 $y$ 与预测值 $\hat{y}$ 之间差值的平方和，通过使损失达到最小，从而选出最能够拟合数据的权重。
 
 ![[1. 线性模型（Linear Model）#损失函数 Loss Function 与误差函数 Cost Function]]
```
end
<!--SR:!2025-08-19,11,285-->

## 思考下学习率，梯度，梯度下降之间的关系
?
```ad-success
title: 答案
collapse: close
 梯度下降的目的是为了调整权重$\omega$，使得损失降至最低，从而选择出最拟合的函数。梯度下降的公式可表示为：
 $$
\omega_{1} = \omega - \alpha \frac{\partial cost}{\partial \omega}
$$
其中的学习率$\alpha$决定了下一权重$\omega_1$的大小，梯度就是当前损失对于当前权重求导得到的导数值


```
[[2. 梯度下降算法（Gradient Descent Algorithm）#梯度下降的基本原理]]
end
<!--SR:!2025-08-18,10,285-->

## 使用梯度下降的目的是什么，梯度下降有什么局限性，什么是随机梯度下降？
?
```ad-success
title: 答案
collapse: close
首先，先理解什么是梯度，梯度的方向指明了函数变化最快的方向。
对于简单的线性模型，可以通过损失对权重的导函数$\frac{\partial loss}{\partial \omega}$，使其为0，也就是直接求出刚刚好可以拟合所有数据点的权重$\omega$，但是对于==复杂的非线性模型==是找不到这么完美的权重的，所以就有了==梯度下降==，就像下山一样（往梯度最小的方向走），逐步的更新权重，==找到使得损失最小的值==（走到山底）。

简单的使用梯度下降，会导致当前的损失值陷入鞍点，而无法更新权重，所以解决方法是取一小批次的梯度来求均值用于更新下一权重的值

![[2. 梯度下降算法（Gradient Descent Algorithm）#^ycsbf4]]
```
end
<!--SR:!2025-08-11,3,267-->

## 什么是线性模型，什么是非线性模型？
?
```ad-success
title: 答案
collapse: close
![[1. 线性模型（Linear Model）#线性模型的局限]] 
```
end
<!--SR:!2025-08-20,12,285-->

## 反向传播的目的是什么呢？
?
```ad-success
title: 答案
collapse: close
 反向传播算法的目的是有效地计算出所有参数的梯度，为优化算法（如梯度下降）提供**方向和大小**，指导网络参数如何调整，以最小化损失函数。
```
end
<!--SR:!2025-08-11,3,267-->

## 什么是独热编码？为什么需要独热编码
?
```ad-success
title: 答案
collapse: close
![[独热编码（One-Hot Encoding）]]
```
end

## 什么是词嵌入，为什么需要词嵌入
?
```ad-success
title: 答案
collapse: close
![[词嵌入（word embedding）]]
```
end
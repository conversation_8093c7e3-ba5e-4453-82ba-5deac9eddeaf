---
tags:
  - 学习
  - deep_learning
  - pytorch
---
# 模型训练目标与挑战

在训练神经网络的时候，核心目标是找到最优的 [[1. 线性模型（Linear Model）#模型设计|权重]] ($\omega$)，使 [[2. 梯度下降算法（Gradient Descent Algorithm）#^bwp6bp|loss]] 降至最低。
对于一个简单的线性模型 ：
$$\hat{y} = \omega * x $$ 
我们可以通过推到损失函数对于权重的导数（梯度）来找到最优的权重值。
$$
 \\
\begin{Bmatrix}
\omega = \omega - \alpha \frac{\partial loss}{\partial \omega} \\
\frac{\partial loss}{\partial \omega} = \frac{\partial (\hat{y}-y)^2}{\partial \omega} = \frac{\partial (\omega * x - y)^2}{\partial \omega} = 2 * (\omega * x - y) * x
\end{Bmatrix}
$$
- 其中 $\hat{y}$ 是预测值，$y$ 是真实值，$x$ 是输入值

然而对于复杂的神经网络模型，具有多个层和多个参数，直接推导每个参数的梯度是非常复杂的。为了解决这个问题，我们使用了反向传播算法（Back Propagation Algorithm）

## 反向传播算法（Back Propagation Algorithm）

### 计算图

**计算图 (Computational Graph)** 是一种将数学表达式或计算过程可视化的工具。在神经网络中，它清晰地展示了数据从输入到输出的流动路径，以及中间涉及的每一个运算步骤。
当使用多层激活函数嵌套的时候，输出和输出中间存在的嵌套的变换过程对外相当于不可见的，被称之为隐藏层
```ad-flex
title: 计算图
color: 255,150,128
<div>

**单输入计算图**：![[学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md#^clippedframe=aQ-sge03Z_UIPt-QVF99b]]

- **图示解读：** 箭头表示数据流向：x 作为输入，经过 g 的处理，得到 y。
        
- **数学公式：** f(x) = g(ωx + b)
    
- **概念解释：**
    
    1. **线性变换：** 输入 x 首先乘以一个**权重 (weight)** ω，然后加上一个**偏置 (bias)** b，得到 ωx + b。这部分是线性计算。
        
    2. **非线性激活：** 线性计算的结果被送入一个**激活函数 (activation function)** g 中进行处理。激活函数（如 Sigmoid、ReLU 等）为模型引入了非线性能力，使其能学习更复杂的模式。
        
    3. **输出：** 最终的输出为 y。

</div>

<div>

**多输入计算图**：![[学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md#^clippedframe=edve9gsPSBTBb9XkuUibh]]

- **图示解读：** 这个模型展示了当一个神经元接收多个输入时的情景。
    
- **数学公式：** f(x1, x2) = g(ω1x1 + ω2x2 + b)
    
- **概念解释：**
    
    1. **加权求和 (Weighted Sum):** 每个输入 xi 都与它对应的权重 ωi 相乘，然后将所有结果相加，最后再加上一个共享的偏置 b。这被称为输入的“加权求和”。
        
    2. **激活与输出：** 加权求和的结果同样经过激活函数 g 的处理，得到最终输出 y。

</div>

<div>

**嵌套计算图**：![[学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md#^clippedframe=AeTpUEzvFNOxLsTkZLH7U]]

- **图示解读：**
    
    - 这张图展示了如何将多个计算节点连接起来，形成一个**层次化**的结构。
        
    - **输入层:** 两个输入节点 x1, x2。
        
    - **隐藏层:** 一个中间计算节点 g1。它接收来自输入层的数据。
        
    - **输出层:** 最终的输出节点 y。它接收来自**隐藏层**的数据作为自己的输入。
        
- **数学公式：** f(x1, x2) = g(ω3 * g(ω1x1 + ω2x2 + b1) + b2)
    
- **概念解释：**
    
    1. **层次结构：** 这张图的核心思想是**“函数的嵌套”**，这也是深度学习中“深度”的来源，这使得神经网络能够学习和表示远比浅层网络复杂得多的函数和模式。
        
    2. **隐藏层：** 节点 g1 是一个真正的**隐藏层 (Hidden Layer)**。它的关键特征是：它的计算结果**不是**最终输出，而是作为下一层计算的**输入**。它的存在对外部是“隐藏”的。
        
    3. **输出层：** 节点 y 是**输出层 (Output Layer)**，它接收来自隐藏层的数据并计算出最终结果。

</div>


```

### 激活函数的重要性

对于一个神经网络来说，如果没有[[学习库/Deep learning/概念库/激活函数]]的话，无论经过多少层的线性变换，本质上都可看成一层线性变换，这样就相当于始终为线性函数，根本无法使得函数**拟合复杂数据**。
- **线性变换**：![[学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md#^group=DiIaPrOnqRqSp-jbgjJv-|600]]
- **多层线性变换**：![[学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md#^group=812yav_Y7D1L6Qf04ENhi|600]]
因此，为了提高模型的表达能力，我们需要使用 [[学习库/Deep learning/概念库/激活函数| 非线性激活函数]] (Non-activation function，可以简称为激活函数)，将线性模型变成非线性模型，使得模型可以表述复杂的关系（让函数变弯，拟合复杂数据）。![[学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md#^group=NAwA27s8DIKp9Ro2LIfpD|600]]

反向传播算法的核心思想是使用**计算图（Computational Graph**）来表示神经网络的计算过程。计算图是一个**有向无环图（Directed Acyclic Graph, DAG**），其中节点表示操作或变量，边表示数据流动。




### 链式法则（Chain Rule）

- 链式法则是微积分中的一个重要法则，用于计算复合函数的导数，例如$f(g(x))$的导数可以表示为$f' =\frac{\partial f}{\partial g} \cdot \frac{\partial g}{\partial x}$
- 在反向传播算法中，需要==计算损失函数对当前层输入的梯度==，通过使用链式法则来计算最终损失 (loss) 对当前层输出的梯度（求导）与当前层输出对当前层输入的梯度（局部导数）的乘积。

#### 正向传播（Forward Propagation）

- 正向传播是指将输入数据通过神经网络的每一层进行计算（沿着计算图的箭头方向），直到得到最终的==输出结果==和==损失值（Loss）==。
- 在正向传播的过程中，除了==计算输出值==，还可以==同时计算每一层的梯度值==（每一层输出对于输入的梯度，局部导数），这些梯度值在反向传播中会用到。


```ad-example
title: 线性模型的正向传播
![](./attachments/3.%20反向传播（Back%20Propagation）-2025-08-03,10-08,10-06-10.webp)

如图所示，对于这个线性模型 $y = \omega \times x$ ，其输入是 $x = 1$，权重 $\omega = 1$ 
- 第一层：输出为：$\hat{y} = x * \omega = 1$ ， 同时计算梯度：$\frac{\hat{y}}{\partial \omega}= \frac{\partial{x * \omega}}{\partial{\omega}} = x = 1$
- 第二层：输出为：$r=\hat{y}-y = -1$， 同时计算梯度：$\frac{\partial{r}}{\partial{\hat{y}}} = \frac{\partial{(\hat{y}-y})}{\partial{\hat{y}}} = 1$
- 最终输入：$loss = r^{2} = 1$

```
^w7plgs


#### 反向传播（Backward Propagation）

- 在正向传播完成后，我们会得到最终的损失值（Loss），接下来我们需要通过反向传播算法来计算损失函数对每一层输入的梯度，最终得到损失函数对于输入的梯度（$\frac{\partial{loss}}{\omega}$），然后完成参数（$\omega$）的更新 [[2. 梯度下降算法（Gradient Descent Algorithm）#随机梯度下降|随机梯度下降]]
- 可以使用链式法则将损失函数对每一层输入的梯度表示为==损失函数对当前层输出的梯度==与==当前层输出对当前层输入的梯度（[[3. 反向传播（Back Propagation）#^w7plgs|在正向传播的时候计算的]]）==的==乘积==

```ad-example
title: 线性模型的梯度下降
![](./attachments/3.%20反向传播（Back%20Propagation）-2025-08-03,10-08,10-12-57.webp)
如图所示：
- loss对r的梯度：$\frac{\partial{loss}}{r} = \frac{r^{2}}{r} = 2r = -2$
- loss对$\hat{y}$的梯度，使用链式法则可表示为：$\frac{\partial{loss}}{\hat{y}} = \frac{\partial{loss}}{r} * \frac{\partial{r}}{\hat{y}} = -2 * 1 = -2$
- loss对$\omega$的梯度，使用链式法则可表示为：$\frac{\partial{loss}}{\omega} = \frac{\partial{loss}}{\hat{y}} * \frac{\partial{\hat{y}}}{\omega} = -2 * x = -2$

这里算出来loss对w的导数为负数，表面当前的方向的w会使得loss变小，根据[[2. 梯度下降算法（Gradient Descent Algorithm）#梯度下降的基本原理|使用梯度下降更新权重得公式]]，可以知道新得权重会变大
```



#### 练习

对于加入偏置(bias)的仿射模型(Affine Model) $y = \omega * x + b$，请计算损失函数对权重和偏置的梯度

![[Lecture_04_Back_Propagation.pdf#page=34&rect=13,46,920,474&color=red|Lecture_04_Back_Propagation, p.34]]

- 正向传播：
    - 第一层：$x * \omega  = 1 * 1 = 1$, $\frac{\hat{y}}{\partial \omega}= \frac{\partial{x * \omega}}{\partial{\omega}} = x = 1$
    - 第二层：$\hat{y} = x * \omega + b = 1 + 2 = 3$, $\frac{\partial{\hat{y}}}{\partial{\omega * x}} = \frac{\partial({\omega * x}+b)}{\partial{\omega * x}} = 1$，$\frac{\partial{\hat{y}}}{\partial{b}} = \frac{\partial({\omega * x}+b)}{\partial{b}} = 1$
    - 最终输入：$loss = r^{2} = (\hat{y} - y)^2 = 1$

- 反向传播：
    - loss 对 $\hat{y}$ 的梯度：$\frac{\partial{loss}}{\hat{y}} = \frac{\partial{loss}}{r} * \frac{\partial{r}}{\hat{y}} = 2 * 1 = 2$
    - loss 对 $\omega * x$ 的梯度：$\frac{\partial{loss}}{\hat{y}} * \frac{\partial{\hat{y}}}{\omega * x} = 2 * 1 = 2$
    - loss 对 $\omega$ 的梯度：$\frac{\partial{loss}}{\omega * x} * \frac{\partial{\omega * x}}{\omega} = 2 * 1 = 2$ 
    - loss 对 $b$ 的梯度：$\frac{\partial{loss}}{\partial{\hat{y}}} * \frac{\partial{\hat{y}}}{b} = 2 * 1 = 2$

## excalidraw

[[3. 反向传播（Back Propagation）.excalidraw]]






{"folderPresets": [{"type": "DEFAULT", "name": "Default (Obsidian setting)"}, {"type": "ROOT", "name": "Root folder"}, {"type": "CURRENT", "name": "Same folder as current note"}], "selectedFolderPreset": "Default (Obsidian setting)", "filenamePresets": [{"name": "Keep original name", "customTemplate": "{imagename}", "skipRenamePatterns": "", "conflictResolution": "increment"}, {"name": "NoteName-Timestamp", "customTemplate": "{notename}-{timestamp}", "skipRenamePatterns": "", "conflictResolution": "increment"}, {"name": "{notename}-{date}-{HH}-{mm}", "customTemplate": "{notename}-{date},{HH}-{MM},{HH}-{mm}-{ss}", "skipRenamePatterns": ""}], "selectedFilenamePreset": "{notename}-{date}-{HH}-{mm}", "outputFormat": "NONE", "quality": 100, "colorDepth": 1, "pngquantQuality": "65-80", "ffmpegExecutablePath": "", "ffmpegCrf": 23, "ffmpegPreset": "medium", "resizeMode": "None", "desiredWidth": 800, "desiredHeight": 600, "desiredLongestEdge": 1000, "enlargeOrReduce": "Auto", "allowLargerFiles": false, "showPresetModal": {"folder": false, "filename": false}, "subfolderTemplate": "", "conversionPresets": [{"name": "None", "outputFormat": "NONE", "quality": 100, "colorDepth": 1, "resizeMode": "None", "desiredWidth": 800, "desiredHeight": 600, "desiredLongestEdge": 1000, "enlargeOrReduce": "Auto", "allowLargerFiles": false, "skipConversionPatterns": "", "pngquantExecutablePath": "", "pngquantQuality": "65-80", "ffmpegExecutablePath": "", "ffmpegCrf": 23, "ffmpegPreset": "medium"}, {"name": "WEBP (75, no resizing)", "outputFormat": "WEBP", "quality": 75, "colorDepth": 1, "resizeMode": "None", "desiredWidth": 800, "desiredHeight": 600, "desiredLongestEdge": 1000, "enlargeOrReduce": "Auto", "allowLargerFiles": false, "skipConversionPatterns": "", "pngquantExecutablePath": "", "pngquantQuality": "65-80", "ffmpegExecutablePath": "", "ffmpegCrf": 23, "ffmpegPreset": "medium"}, {"name": "PNGQUANT (65-80, no resizing)", "outputFormat": "ORIGINAL", "quality": 65, "colorDepth": 1, "resizeMode": "Fit", "desiredWidth": 800, "desiredHeight": 600, "desiredLongestEdge": 1000, "enlargeOrReduce": "Auto", "allowLargerFiles": false, "skipConversionPatterns": "*.gif", "pngquantExecutablePath": "", "pngquantQuality": "65-80", "ffmpegExecutablePath": "", "ffmpegCrf": 23, "ffmpegPreset": "medium"}], "selectedConversionPreset": "PNGQUANT (65-80, no resizing)", "globalPresets": [{"name": "WebP 75", "folderPreset": "Default (Obsidian setting)", "filenamePreset": "NoteName-Timestamp", "conversionPreset": "WEBP (75, no resizing)", "linkFormatPreset": "De<PERSON>ult (Wikilink, Shortest)", "resizePreset": "Default (No Resize)"}], "selectedGlobalPreset": "", "linkFormatSettings": {"linkFormatPresets": [{"name": "De<PERSON>ult (Wikilink, Shortest)", "linkFormat": "wikilink", "pathFormat": "shortest", "prependCurrentDir": false, "hideFolders": false}, {"name": "<PERSON><PERSON>, Relative", "linkFormat": "markdown", "pathFormat": "relative", "prependCurrentDir": true, "hideFolders": false}], "selectedLinkFormatPreset": "<PERSON><PERSON>, Relative"}, "nonDestructiveResizeSettings": {"resizePresets": [{"name": "Default (No Resize)", "resizeDimension": "none", "resizeScaleMode": "auto", "respectEditorMaxWidth": true, "maintainAspectRatio": true, "resizeUnits": "pixels"}, {"name": "Width 500px", "resizeDimension": "width", "width": 500, "resizeScaleMode": "auto", "respectEditorMaxWidth": true, "maintainAspectRatio": true, "resizeUnits": "pixels"}, {"name": "Height 800px", "resizeDimension": "height", "height": 800, "resizeScaleMode": "auto", "respectEditorMaxWidth": true, "maintainAspectRatio": true, "resizeUnits": "pixels"}, {"name": "50% Width", "resizeDimension": "width", "width": 50, "resizeScaleMode": "auto", "respectEditorMaxWidth": true, "maintainAspectRatio": true, "resizeUnits": "percentage"}, {"name": "Longest Edge 1000px", "resizeDimension": "longest-edge", "longestEdge": 1000, "resizeScaleMode": "auto", "respectEditorMaxWidth": true, "maintainAspectRatio": true, "resizeUnits": "pixels"}, {"name": "Fit Editor", "resizeDimension": "editor-max-width", "resizeScaleMode": "auto", "respectEditorMaxWidth": true, "maintainAspectRatio": true, "resizeUnits": "pixels"}, {"name": "Original Width", "resizeDimension": "original-width", "resizeScaleMode": "auto", "respectEditorMaxWidth": false, "maintainAspectRatio": true, "resizeUnits": "pixels"}, {"name": "Custom (Distort)", "resizeDimension": "both", "customValue": "300x100", "resizeScaleMode": "auto", "respectEditorMaxWidth": false, "maintainAspectRatio": false, "resizeUnits": "pixels"}], "selectedResizePreset": "Default (No Resize)"}, "resizeCursorLocation": "none", "dropPasteCursorLocation": "back", "neverProcessFilenames": "*.excalidraw.md， *.excalidraw, excalidraw/, /excalidraw/, /.*excalidraw.*/\n*.gif,\n/.gif", "modalBehavior": "never", "singleImageModalSettings": {"conversionPresetName": "None", "outputFormat": "WEBP", "quality": 39, "colorDepth": 1, "resizeMode": "None", "desiredWidth": 800, "desiredHeight": 600, "desiredLongestEdge": 1000, "enlargeOrReduce": "Auto", "allowLargerFiles": false, "pngquantExecutablePath": "", "pngquantQuality": "65-80", "ffmpegExecutablePath": "", "ffmpegCrf": 23, "ffmpegPreset": "medium"}, "ProcessCurrentNoteconvertTo": "webp", "ProcessCurrentNotequality": 0.75, "ProcessCurrentNoteResizeModalresizeMode": "None", "ProcessCurrentNoteresizeModaldesiredWidth": 600, "ProcessCurrentNoteresizeModaldesiredHeight": 800, "ProcessCurrentNoteresizeModaldesiredLength": 800, "ProcessCurrentNoteskipImagesInTargetFormat": false, "ProcessCurrentNoteEnlargeOrReduce": "Always", "ProcessCurrentNoteSkipFormats": "tif,tiff,heic", "ProcessAllVaultconvertTo": "disabled", "ProcessAllVaultquality": 0.75, "ProcessAllVaultResizeModalresizeMode": "None", "ProcessAllVaultResizeModaldesiredWidth": 500, "ProcessAllVaultResizeModaldesiredHeight": 500, "ProcessAllVaultResizeModaldesiredLength": 500, "ProcessAllVaultEnlargeOrReduce": "Always", "ProcessAllVaultSkipFormats": "", "ProcessAllVaultskipImagesInTargetFormat": false, "annotationPresets": {"drawing": [{"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 2}, {"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 2}, {"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 2}], "arrow": [{"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 8}, {"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 8}, {"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 8}], "text": [{"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 24, "backgroundColor": "transparent", "backgroundOpacity": 0.7}, {"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 24, "backgroundColor": "transparent", "backgroundOpacity": 0.7}, {"color": "#000000", "opacity": 1, "blendMode": "source-over", "size": 24, "backgroundColor": "transparent", "backgroundOpacity": 0.7}]}, "isImageAlignmentEnabled": true, "imageAlignment_cacheCleanupInterval": 3600000, "imageAlignment_cacheLocation": ".obsidian", "isDragResizeEnabled": false, "isScrollResizeEnabled": true, "isDragAspectRatioLocked": false, "isResizeInReadingModeEnabled": false, "resizeSensitivity": 0.1, "scrollwheelModifier": "Shift", "isImageResizeEnbaled": true, "resizeState": {"isResizing": false}, "enableContextMenu": true, "showSpaceSavedNotification": true, "revertToOriginalIfLarger": false, "enableImageCaptions": false, "skipCaptionExtensions": "icns, .excalidraw.md, .excalidraw", "captionFontSize": "var(--font-smaller)", "captionColor": "var(--text-gray)", "captionFontStyle": "italic", "captionBackgroundColor": "transparent", "captionPadding": "2px 4px", "captionBorderRadius": "0", "captionOpacity": "1", "captionFontWeight": "normal", "captionTextTransform": "none", "captionLetterSpacing": "normal", "captionBorder": "none", "captionMarginTop": "4px", "captionAlignment": "center"}
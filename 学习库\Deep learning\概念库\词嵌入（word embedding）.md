---
tags:
  - 学习
  - Artifical_Intelligence
---
# 词嵌入（word embedding）

词嵌入可以解决 [[独热编码（One-Hot Encoding）#独热编码的缺点|独热编码]] 中词和词之间缺乏相关性的问题，其核心思想是：**将每个词映射**到一个低维、稠密的向量空间中。在这个空间中，**语义上相似的词，其对应的向量在空间中的位置也更接近。**

```ad-col2
title: 词嵌入举例
color:178,22,164
collapse: close

<div>

比如说：
- **"猫"** 的词嵌入可能是：`[0.8, 0.2]`
- **"狗"** 的词嵌入可能是：`[0.7, 0.3]`
- **"喜欢"** 的词嵌入可能是：`[-0.1, 0.9]`
- **"我"** 的词嵌入可能是：`[-0.5, -0.4`

观察这些向量：
- "猫"和"狗"的向量非常相似，因为它们都是宠物，在语义上很接近。我们可以通过计算它们之间的**余弦相似度**来衡量这种相似性，余弦相似度越接近1，表示越相似。
- "喜欢"的向量与"猫"和"狗"的向量有较大差异，因为它是一个动词，表达的是一种情感。
- "我"的向量则代表了一个代词，与其他词的语义关系又不同。

</div>

![[学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md#^clippedframe=1Kqy8rxSZFAYU0rShZsGb]]
```

每一个词语都有一个对应的特征向量，而这些向量不像 [[独热编码（One-Hot Encoding）|one-hot]] 中那样要人为的设计特征，而是通过深度学习的方式自动划分的。



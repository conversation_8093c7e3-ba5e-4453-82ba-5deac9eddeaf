---
tags:
  - 学习
  - deep_learning
  - pytorch
---


# 5. 逻辑回归（Logistic Regression）

前面介绍了==线性模型（Linear Model）==，它的输出是一个实数值，==适用于回归问题（Regression Problem）==。但是在==分类问题（Classification Problem）==中，我们需要将==输出值限制在0到1之间==，以便进行概率预测。为了解决这个问题，可以使用逻辑回归（Logistic Regression）。
逻辑回归是一种分类算法，它通过将线性模型的输出值经过一个非线性函数（[[学习库/Deep learning/概念库/激活函数#Sigmoid|Sigmoid函数]] ）来实现分类。其输出并不是直接输出一个输入是不是属于某个类别，而是输出一个概率值，表示==输入属于某个类别的概率==。

![[5. 逻辑回归（Logisitic Regression)-2025-04-30-08-58-20.png]]

## 分类任务

分类任务的目标是根据输入数据的特征，将其分配到预定义的类别 (Category) 或标签 (Label) 中的某一个或多个。与预测连续值（如房价、温度）的回归任务不同，分类任务预测的是离散的类别。

### 分类任务的类型

- **二元分类 (Binary Classification)**：只有两个可能的类别
  - 例子：
    - 判断一封邮件是“垃圾邮件”还是“非垃圾邮件”。
    - 判断一张图片中是否包含“猫”。
    - 判断一个客户是否会“流失”。

- **多类分类 (Multi-class Classification)**：有三个或更多可能的类别，但每个==输入样本只能属于其中一个类别==。这些类别是互斥的
  - 例子:
    - 手写数字识别（类别：0, 1, 2, 3, 4, 5, 6, 7, 8, 9）。一张图片只能代表一个数字。
    - 图像物体识别（类别：猫、狗、汽车、飞机）。一张图片主要被归类为一个物体（在简单设定下）。
    - 新闻主题分类（类别：体育、科技、娱乐、财经）。一篇文章通常归属于一个主要主题。

- **多标签分类 (Multi-label Classification)**：有三个或更多可能的类别，但==每个输入样本可以同时属于多个类别==。这些类别不是互斥的。
  - 例子：
    - 电影类型标注（类别：动作、喜剧、科幻、爱情）。一部电影可以是“动作”片，同时也是“科幻”片。
    - 图片内容标注（类别：人、狗、公园、白天）。一张图片可能同时包含这些元素。
    - 文章标签（类别：机器学习、Python、数据科学）。一篇文章可能涉及多个标签


## 分类损失函数

衡量模型预测结果与真实标签之间的“差距”或“错误程度”。训练的目标就是通过调整模型的参数（权重和偏置），来最小化这个损失函数的值，从而让模型的预测越来越接近真实情况。

### 交叉熵损失函数（Cross-Entropy Loss）

前面的回归问题中，我们使用 [[1. 线性模型（Linear Model）#损失函数 Loss Function 与误差函数 Cost Function|均方误差（MSE）]]作为损失函数来衡量模型的预测值与真实值之间的差距。而在分类问题中，我们需要使用交叉熵损失函数（Cross-Entropy Loss）来衡量模型的预测概率与真实标签之间的差距。

- **二元分类交叉熵损失函数（Binary Cross-Entropy Loss）**： ^f27jm1
  - **用途**：用于二元分类问题
  - **要求**：模型的最后一层通常需要一个 [[学习库/Deep learning/概念库/激活函数#Sigmoid|Sigmoid函数]] ，输出一个概率值 p（0到1之间），表示输入属于正类的概率。真实标签为 0 或 1。
  - **公式**：$L(y, \hat{y}) = -y \log(\hat{y}) - (1 - y) \log(1 - \hat{y})$
  - **解释**：
    - 当真实标签 y=1 时，损失函数为 $-\log(\hat{y})$，表示模型预测的概率越接近 1，损失越小。
    - 当真实标签 y=0 时，损失函数为 $-\log(1 - \hat{y})$，表示模型预测的概率越接近 0，损失越小。
  - **举例**（垃圾邮件检测）：
    - 如果一封邮件被预测为垃圾邮件的概率为 0.9（即 $\hat{y} = 0.9$，模型认为是垃圾邮件的概率为 90%），而真实标签为 $y=1$（即垃圾邮件），则损失为 $-\log(0.9) \approx 0.105$，损失较小。
    - 如果一封邮件被预测为垃圾邮件的概率为 0.1（即 $\hat{y} = 0.1$，模型认为是垃圾邮件的概率为 10%），而真实标签为 $y=1$（即垃圾邮件），则损失为 $-\log(1 - 0.1) \approx 2.303$，损失很大。

- **多类分类交叉熵损失函数（Categorical Cross-Entropy Loss）**：
  - **用途**：用于多类分类问题
  - **要求**：真实标签通常需要进行[[独热编码（One-Hot Encoding）|独热编码（One-Hot Encoding）]]，模型的最后一层通常需要一个 [[学习库/Deep learning/概念库/激活函数#Softmax|Softmax函数]] ，输出一个概率分布$p$，表示每个类别的概率，其中每个元素$p_{i}$ 表示输入属于第$i$类的概率，且所有类别的概率之和为 1。
  - **公式**：$L(y, \hat{y}) = -\sum_{i=1}^{C} y_i \log(\hat{y}_i)$
  - **解释**：
    - 其中 $C$ 是类别的数量，$y_i$ 是真实标签的独热编码，$\hat{y}_i$ 是模型预测的概率。
    - 损失函数的值越小，表示模型的预测概率与真实标签之间的差距越小。
  - **举例**（手写数字识别，有三个类别分别为: 0, 1, 2）：
    - 如果真实标签为 $y = [0, 1, 0]$（经过独热编码后的标签，表示真实标签是：`类别 1`），模型预测的概率为 $\hat{y} = [0.1, 0.7, 0.2]$，则损失为 $L(y, \hat{y}) = -[0 \cdot \log(0.1) + 1 \cdot \log(0.7) + 0 \cdot \log(0.2)] = -\log(0.7) \approx 0.357$。
    - 如果真实标签为 $y = [1, 0, 0]$（经过独热编码后的标签，表示真实标签是：`类别0`），模型预测的概率为 $\hat{y} = [0.8, 0.1, 0.1]$，则损失为 $L(y, \hat{y}) = -[1 \cdot \log(0.8) + 0 \cdot \log(0.1) + 0 \cdot \log(0.1)] = -\log(0.8) \approx 0.223$。

- **多标签分类交叉熵损失函数（ Multi-label Classification Cross-Entropy with Logits Loss）**：
  - **用途**：用于多标签分类问题
  - **要求**：通常直接对每一个类别分别进行二元分类，模型的最后一层通常需要一个 [[学习库/Deep learning/概念库/激活函数#Sigmoid|Sigmoid函数]] ，输出一个概率值$p$，表示输入属于正类的概率。
  - **公式**：$L(y, \hat{y}) = -\frac{1}{N} \sum_{i=1}^{N} [y_i \log(\hat{y}_i) + (1 - y_i) \log(1 - \hat{y}_i)]$
  - **解释**：
    - 其中 $N$ 是样本的数量，$y_i$ 是真实标签，$\hat{y}_i$ 是模型预测的概率。
    - 损失函数的值越小，表示模型的预测概率与真实标签之间的差距越小。
  - **举例**（电影类型）：
    - 假设有三个类别（动作、喜剧、科幻），真实标签为 $y = [1, 0, 1]$（表示真实标签是动作和科幻），模型预测的概率为 $\hat{y} = [0.8, 0.2, 0.6]$，分别计算每个类别的 [[5. 逻辑回归（Logisitic Regression)#^f27jm1|BCE Loss]] ，进行累加后求均值，则损失为$L(y, \hat{y}) = -\frac{1}{3}[(1 \cdot \log(0.8) + (1 - 1) \cdot \log(0.2)) + (0 \cdot \log(0.2) + (1 - 0) \cdot \log(1 - 0.2)) + (1 \cdot \log(0.6) + (1 - 1) \cdot \log(1 - 0.6))]$

## 在`pytorch`中实现逻辑回归

如下图所示，在定义逻辑回归模型时，初始化与线性模型的初始化是一样的，就是在前向传播中使用Sigmoid函数。
![[5. 逻辑回归（Logisitic Regression)-2025-04-30-10-54-27.png]]


```python
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F

x_train = torch.tensor([[1.0], [2.0], [3.0], [4.0]])  # 训练数据，形状为(batch_size, 1)
y_train = torch.tensor([[0.0], [0.0], [1.0], [1.0]])  # 训练标签，形状为(batch_size, 1)

# 定义逻辑回归模型
class LogisticRegressionModel(nn.Module):
    def __init__(self):
        super(LogisticRegressionModel, self).__init__()
        self.linear = nn.Linear(1, 1)  # 线性层

    def forward(self, x):
        y_pred = self.linear(x)  # 前向传播
        y_pred = torch.sigmoid(y_pred)  # Sigmoid激活函数
        return y_pred

# 实例化模型
model = LogisticRegressionModel()

# 损失函数
criterion = nn.BCELoss()  # 二元交叉熵损失函数`
# 优化器
optimizer = optim.SGD(model.parameters(), lr=0.01)  # 随机梯度下降优化器，lr是学习率

# 训练模型
num_epochs = 1000  # 训练轮数
for epoch in range(num_epochs):
    # 前向传播
    y_pred = model(x_train)  # x_train是训练数据，形状为(batch_size, 1)
    # 计算损失函数
    loss = criterion(y_pred, y_train)  # y_train是训练标签，形状为(batch_size, 1)
    # 清空梯度
    optimizer.zero_grad()
    # 反向传播
    loss.backward()  # 计算梯度
    # 更新权重
    optimizer.step()  # 更新权重
    # 打印损失
    if (epoch + 1) % 100 == 0:
        print(f'Epoch [{epoch + 1}/{num_epochs}], Loss: {loss.item():.4f}')
# 打印权重和偏置
print(f'Weight: {model.linear.weight.item():.4f}, Bias: {model.linear.bias.item():.4f}')
```

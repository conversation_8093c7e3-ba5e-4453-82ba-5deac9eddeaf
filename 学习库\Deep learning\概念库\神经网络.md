---
tags:
  - 学习
  - Artifical_Intelligence
---

# 神经网络


## 单层神经网络
简单的神经网络的模型如下图所示，其中隐藏层的作用是让模型更加的复杂，也就是将输入数据进行升维（这里的隐藏层有 10 个神经元，也就是将输入层从原来的二维升到了 10 维）。
![[Pasted image 20241008095639.png]]

## 多层神经网络
而对于下面这张人脸图片，输入层就是一张图片中的所有像素，隐藏层可以表示为对不同像素进行综合判断的各个动作特征，最后在输出层中进行综合判断得到人的心情 ![[Pasted image 20241008100523.png]] 但这样是无法解释为什么神经网络需要这么多隐藏层。下面的 GIF 是数字识别任务，输入同样是图片的各个像素，对于一个数字 8可以看出它最主要的特征是上面和下面的两个圆圈，这两个圆圈又分别由各自的四个子特征（浅层特征）所组成，这样的好处就是对于其它的数字，比如数字 6，下面的圈可以和 8 公用子特征，属于它独有的子特征也就只有上面的竖。所以隐藏层越深，神经网络的抽象程度越高。  ![[GIF 2024-10-8 10-21-33.gif]]
## 神经网络的数学模型

```ad-hint
title: 神经网络
collapse: close
![[3. 反向传播（Back Propagation）#计算图]]
```

如上所示，神经网络可以写成形如 $y=g(x)$ 的形式，但是如果有多个输入的话，每一个输入输出都有一个对应的函数形式。为了简化就写成矩阵的形式。
```ad-col2
title: 每个神经元的输出和输入对应的函数形式
color:178,22,164
多个输出对应的输入：
$$
\begin{align}
y_1 &= g (\omega_{11}x_1 + \omega_{12}x_1 + \omega_{13}x_1) \\
y_2 &= g (\omega_{21}x_1 + \omega_{22}x_1 + \omega_{23}x_1)
\end{align}
$$
写成矩阵简化：
$$
\begin{align}
\begin{bmatrix}
y_1 \\
y_2
\end{bmatrix}
&=
\begin{bmatrix}
w_{11} \quad w_{12} \quad w_{13}\\
w_{21} \quad w_{22} \quad w_{23}
\end{bmatrix}
\begin{bmatrix}x_1\\x_2\\x_3
\end{bmatrix}
+
\begin{bmatrix}
b_1 \\[1em]
b_2
\end{bmatrix} \\[1.5em]
Y &= \omega X + b
\end{align}
$$
![](./attachments/神经网络-2025-08-09,08-08,08-54-38.png)
```

但是上面的公式无法表示**层级**，所以如果有多个层级的就可以**用上标**来表示，每一层的神经元都是上一层的函数：
```ad-col2
title: 多层神经元
color:178,22,164
第一层神经元：
$$
A^{[1]}=g(W^{[1]}A^{[0]}+b^{[1]})
$$

第L层神经元：
$$
A^{[L]}=g(W^{[L]}A^{[L-1]}+b^{[L]})
$$
![](./attachments/神经网络-2025-08-09,08-08,08-48-42.png)
```

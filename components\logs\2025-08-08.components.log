2025-08-08 17:06:59.446 [info] components database created cost 0 ms   
2025-08-08 17:06:59.447 [info] components index initializing...   
2025-08-08 17:06:59.726 [info] start to batch put pages: 5   
2025-08-08 17:06:59.757 [info] batch persist cost 5  31 
2025-08-08 17:06:59.807 [info] components index initialized, 1067 files cost 361 ms   
2025-08-08 17:06:59.808 [info] refresh page data from init listeners 0 1067   
2025-08-08 17:07:03.814 [info] indexing created file components/logs/2025-08-08.components.log  [object Object] 
2025-08-08 17:07:03.820 [info] refresh page data from created listeners 0 1068   
2025-08-08 17:07:03.957 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 17:07:04.252 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 17:07:04.754 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-08 17:07:04.758 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-08 17:07:04.762 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-08 17:07:04.766 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-08 17:07:52.979 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:07:53.010 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:07:53.011 [info] index finished after resolve  [object Object] 
2025-08-08 17:07:53.012 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:08:07.566 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:08:07.582 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:08:07.583 [info] index finished after resolve  [object Object] 
2025-08-08 17:08:07.584 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:08:56.095 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:08:56.109 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:08:56.110 [info] index finished after resolve  [object Object] 
2025-08-08 17:08:56.111 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:10:23.882 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:10:23.895 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:10:23.895 [info] index finished after resolve  [object Object] 
2025-08-08 17:10:23.896 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:11:02.610 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:11:02.619 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:11:02.624 [info] index finished after resolve  [object Object] 
2025-08-08 17:11:02.625 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:11:20.737 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:11:20.750 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:11:20.751 [info] index finished after resolve  [object Object] 
2025-08-08 17:11:20.752 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:12:17.851 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:12:17.866 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:12:17.867 [info] index finished after resolve  [object Object] 
2025-08-08 17:12:17.867 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:12:35.371 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:12:35.383 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:12:35.384 [info] index finished after resolve  [object Object] 
2025-08-08 17:12:35.384 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:14:15.554 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 17:14:15.566 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 17:14:15.567 [info] index finished after resolve  [object Object] 
2025-08-08 17:14:15.567 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:20:53.134 [info] refresh page data from rename listeners 0 1068   
2025-08-08 17:21:04.477 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:04.646 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:04.651 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:04.651 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:06.788 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:06.831 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:06.832 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:06.832 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:08.959 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:09.045 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:09.047 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:09.048 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:35.214 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:35.259 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:35.262 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:35.262 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:37.760 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:37.999 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:38.004 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:38.004 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:47.696 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:47.754 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:47.758 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:47.758 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:49.764 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:49.811 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:49.812 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:49.813 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:53.923 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:54.050 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:54.053 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:54.053 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:21:57.013 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:21:57.195 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:21:57.197 [info] index finished after resolve  [object Object] 
2025-08-08 17:21:57.198 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:22:22.177 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:22:22.259 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:22:22.261 [info] index finished after resolve  [object Object] 
2025-08-08 17:22:22.261 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:22:26.631 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:22:26.692 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:22:26.695 [info] index finished after resolve  [object Object] 
2025-08-08 17:22:26.695 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:22:35.960 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:22:36.092 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:22:36.094 [info] index finished after resolve  [object Object] 
2025-08-08 17:22:36.095 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:22:38.740 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:22:38.856 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:22:38.859 [info] index finished after resolve  [object Object] 
2025-08-08 17:22:38.860 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:23:13.767 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:23:14.045 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:23:14.046 [info] index finished after resolve  [object Object] 
2025-08-08 17:23:14.047 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:23:24.286 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:23:24.487 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:23:24.489 [info] index finished after resolve  [object Object] 
2025-08-08 17:23:24.489 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:23:33.002 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:23:33.059 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:23:33.061 [info] index finished after resolve  [object Object] 
2025-08-08 17:23:33.061 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:23:40.300 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:23:40.392 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:23:40.397 [info] index finished after resolve  [object Object] 
2025-08-08 17:23:40.397 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:23:42.435 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:23:42.496 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:23:42.501 [info] index finished after resolve  [object Object] 
2025-08-08 17:23:42.501 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:23:45.083 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:23:45.269 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:23:45.269 [info] index finished after resolve  [object Object] 
2025-08-08 17:23:45.270 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:28:36.453 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:28:36.621 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:28:36.629 [info] index finished after resolve  [object Object] 
2025-08-08 17:28:36.629 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:34:34.708 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:34:34.714 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:34:34.716 [info] index finished after resolve  [object Object] 
2025-08-08 17:34:34.716 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:34:42.858 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:34:42.870 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:34:42.871 [info] index finished after resolve  [object Object] 
2025-08-08 17:34:42.871 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:34:45.136 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:34:45.142 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:34:45.143 [info] index finished after resolve  [object Object] 
2025-08-08 17:34:45.143 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:34:47.445 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:34:47.453 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:34:47.454 [info] index finished after resolve  [object Object] 
2025-08-08 17:34:47.454 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:34:49.503 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:34:49.510 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:34:49.513 [info] index finished after resolve  [object Object] 
2025-08-08 17:34:49.514 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:03.617 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:03.623 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:03.623 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:03.624 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:05.725 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:05.738 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:05.739 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:05.740 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:08.140 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:08.147 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:08.147 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:08.148 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:10.514 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:10.517 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:10.522 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:10.522 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:18.349 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:18.355 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:18.356 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:18.356 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:20.571 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:20.578 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:20.579 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:20.579 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:22.673 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:22.680 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:22.680 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:22.681 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:24.776 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:24.783 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:24.784 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:24.784 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:26.861 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:26.868 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:26.868 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:26.868 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:31.335 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:31.345 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:31.346 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:31.346 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:33.714 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:33.721 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:33.722 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:33.722 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:35.993 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:35.999 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:36.001 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:36.001 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:39.165 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:39.173 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:39.173 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:39.173 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:43.391 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:43.397 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:43.398 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:43.399 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:45.689 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:45.695 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:45.696 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:45.696 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:48.789 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:48.795 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:48.799 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:48.800 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:53.120 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:53.127 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:53.128 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:53.129 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:55.168 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:55.180 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:55.186 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:55.186 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:35:58.148 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:35:58.155 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:35:58.156 [info] index finished after resolve  [object Object] 
2025-08-08 17:35:58.156 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:36:01.968 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:36:01.975 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:36:01.976 [info] index finished after resolve  [object Object] 
2025-08-08 17:36:01.976 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 17:36:04.552 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 17:36:04.558 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 17:36:04.559 [info] index finished after resolve  [object Object] 
2025-08-08 17:36:04.559 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:53:40.370 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:53:40.381 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:53:40.382 [info] index finished after resolve  [object Object] 
2025-08-08 21:53:40.382 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:53:42.723 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:53:42.730 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:53:42.730 [info] index finished after resolve  [object Object] 
2025-08-08 21:53:42.731 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:53:44.955 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:53:44.961 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:53:44.962 [info] index finished after resolve  [object Object] 
2025-08-08 21:53:44.962 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:54:20.812 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:54:20.834 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:54:20.835 [info] index finished after resolve  [object Object] 
2025-08-08 21:54:20.835 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:54:22.831 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:54:22.850 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:54:22.852 [info] index finished after resolve  [object Object] 
2025-08-08 21:54:22.852 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:54:24.856 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:54:24.863 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:54:24.864 [info] index finished after resolve  [object Object] 
2025-08-08 21:54:24.864 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:54:29.867 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:54:29.881 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:54:29.883 [info] index finished after resolve  [object Object] 
2025-08-08 21:54:29.884 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:54:55.825 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:54:55.832 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:54:55.833 [info] index finished after resolve  [object Object] 
2025-08-08 21:54:55.833 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:54:57.982 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:54:57.991 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:54:57.995 [info] index finished after resolve  [object Object] 
2025-08-08 21:54:57.995 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:00.127 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:00.133 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:00.134 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:00.134 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:14.004 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:14.011 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:14.012 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:14.012 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:20.151 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:20.159 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:20.160 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:20.160 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:25.307 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:25.313 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:25.314 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:25.314 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:38.687 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:38.693 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:38.694 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:38.695 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:40.736 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:40.744 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:40.744 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:40.744 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:43.855 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:43.882 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:43.885 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:43.885 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:45.879 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:45.885 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:45.885 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:45.885 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:53.001 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:53.007 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:53.008 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:53.008 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:56.901 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:56.908 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:56.909 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:56.909 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:55:59.015 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:55:59.021 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:55:59.022 [info] index finished after resolve  [object Object] 
2025-08-08 21:55:59.022 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:56:01.177 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:56:01.184 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:56:01.185 [info] index finished after resolve  [object Object] 
2025-08-08 21:56:01.185 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:56:16.041 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:56:16.047 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:56:16.048 [info] index finished after resolve  [object Object] 
2025-08-08 21:56:16.048 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:56:57.167 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:56:57.324 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:56:57.331 [info] index finished after resolve  [object Object] 
2025-08-08 21:56:57.332 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:57:03.317 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:57:03.494 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:57:03.497 [info] index finished after resolve  [object Object] 
2025-08-08 21:57:03.498 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:57:12.623 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:57:12.633 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:57:12.635 [info] index finished after resolve  [object Object] 
2025-08-08 21:57:12.635 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:57:15.007 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:57:15.014 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:57:15.016 [info] index finished after resolve  [object Object] 
2025-08-08 21:57:15.016 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:57:34.657 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:57:34.666 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:57:34.668 [info] index finished after resolve  [object Object] 
2025-08-08 21:57:34.668 [info] refresh page data from resolve listeners 0 1068   
2025-08-08 21:58:55.351 [info] indexing created file 学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,21-08,21-58-55.webp  [object Object] 
2025-08-08 21:58:55.353 [info] refresh page data from created listeners 0 1069   
2025-08-08 21:58:56.425 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:58:56.616 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:58:56.618 [info] index finished after resolve  [object Object] 
2025-08-08 21:58:56.619 [info] refresh page data from resolve listeners 0 1069   
2025-08-08 21:59:01.387 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 21:59:01.397 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 21:59:01.397 [info] index finished after resolve  [object Object] 
2025-08-08 21:59:01.398 [info] refresh page data from resolve listeners 0 1069   
2025-08-08 21:59:11.854 [info] indexing created file 学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,21-08,21-59-11.webp  [object Object] 
2025-08-08 21:59:11.858 [info] refresh page data from created listeners 0 1070   
2025-08-08 22:00:02.684 [info] indexing created file 学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,22-08,22-00-02.webp  [object Object] 
2025-08-08 22:00:02.685 [info] refresh page data from created listeners 0 1071   
2025-08-08 22:00:04.738 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:00:04.744 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:00:04.744 [info] index finished after resolve  [object Object] 
2025-08-08 22:00:04.745 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:00:07.402 [info] refresh page data from delete listeners 0 1070   
2025-08-08 22:00:07.418 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:00:09.175 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:00:09.347 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:00:09.350 [info] index finished after resolve  [object Object] 
2025-08-08 22:00:09.350 [info] refresh page data from resolve listeners 0 1070   
2025-08-08 22:01:29.013 [info] indexing created file 学习库/Deep learning/概念库/attachments/神经网络的调参-2025-08-08,22-08,22-01-29.gif  [object Object] 
2025-08-08 22:01:29.051 [info] refresh page data from created listeners 0 1071   
2025-08-08 22:01:30.611 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:01:30.789 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:01:30.796 [info] index finished after resolve  [object Object] 
2025-08-08 22:01:30.797 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:01:58.155 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:01:58.309 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:01:58.312 [info] index finished after resolve  [object Object] 
2025-08-08 22:01:58.312 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:02:27.871 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:02:27.877 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:02:27.878 [info] index finished after resolve  [object Object] 
2025-08-08 22:02:27.878 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:02:29.907 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:02:29.913 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:02:29.914 [info] index finished after resolve  [object Object] 
2025-08-08 22:02:29.914 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:02:31.951 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:02:31.957 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:02:31.960 [info] index finished after resolve  [object Object] 
2025-08-08 22:02:31.960 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:02:34.083 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:02:34.095 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:02:34.097 [info] index finished after resolve  [object Object] 
2025-08-08 22:02:34.098 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:02:36.257 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:02:36.290 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:02:36.291 [info] index finished after resolve  [object Object] 
2025-08-08 22:02:36.292 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:02:38.278 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:02:38.286 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:02:38.291 [info] index finished after resolve  [object Object] 
2025-08-08 22:02:38.291 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:03:06.821 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:03:06.911 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:03:06.913 [info] index finished after resolve  [object Object] 
2025-08-08 22:03:06.914 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:03:09.604 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:03:09.727 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:03:09.728 [info] index finished after resolve  [object Object] 
2025-08-08 22:03:09.729 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:03:13.802 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:03:13.884 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:03:13.886 [info] index finished after resolve  [object Object] 
2025-08-08 22:03:13.886 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:03:47.128 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:03:47.135 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:03:47.136 [info] index finished after resolve  [object Object] 
2025-08-08 22:03:47.136 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:03:51.953 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:03:51.959 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:03:51.960 [info] index finished after resolve  [object Object] 
2025-08-08 22:03:51.960 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:04:14.983 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:04:15.116 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:04:15.133 [info] index finished after resolve  [object Object] 
2025-08-08 22:04:15.134 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:04:16.994 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:04:17.000 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:04:17.002 [info] index finished after resolve  [object Object] 
2025-08-08 22:04:17.002 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:04:19.114 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:04:19.121 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:04:19.122 [info] index finished after resolve  [object Object] 
2025-08-08 22:04:19.122 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:06:18.991 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:06:18.998 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:06:18.999 [info] index finished after resolve  [object Object] 
2025-08-08 22:06:18.999 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:07:01.978 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:07:01.984 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:07:01.984 [info] index finished after resolve  [object Object] 
2025-08-08 22:07:01.984 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:07:04.437 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:07:04.443 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:07:04.445 [info] index finished after resolve  [object Object] 
2025-08-08 22:07:04.445 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:07:07.051 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:07:07.059 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:07:07.060 [info] index finished after resolve  [object Object] 
2025-08-08 22:07:07.060 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:07:10.203 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:07:10.215 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:07:10.219 [info] index finished after resolve  [object Object] 
2025-08-08 22:07:10.219 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:07:14.628 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:07:14.635 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:07:14.638 [info] index finished after resolve  [object Object] 
2025-08-08 22:07:14.638 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:07:17.591 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:07:17.599 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:07:17.600 [info] index finished after resolve  [object Object] 
2025-08-08 22:07:17.600 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:07:25.100 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:07:25.106 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:07:25.108 [info] index finished after resolve  [object Object] 
2025-08-08 22:07:25.108 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:08:04.018 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:08:04.024 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:08:04.025 [info] index finished after resolve  [object Object] 
2025-08-08 22:08:04.025 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:08:12.553 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:08:12.658 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:08:12.663 [info] index finished after resolve  [object Object] 
2025-08-08 22:08:12.663 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:08:20.534 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:08:20.541 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:08:20.542 [info] index finished after resolve  [object Object] 
2025-08-08 22:08:20.542 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:08:22.521 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:08:22.633 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:08:22.635 [info] index finished after resolve  [object Object] 
2025-08-08 22:08:22.635 [info] refresh page data from resolve listeners 0 1071   
2025-08-08 22:10:29.163 [info] indexing created file 学习库/Anki/Artificial Intelligence/未命名 2.md  [object Object] 
2025-08-08 22:10:29.163 [info] indexing created ignore file 学习库/Anki/Artificial Intelligence/未命名 2.md   
2025-08-08 22:10:29.393 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 2.md resolve  [object Object] 
2025-08-08 22:10:29.408 [info] index finished after resolve  [object Object] 
2025-08-08 22:10:29.408 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:10:41.267 [info] refresh page data from rename listeners 0 1072   
2025-08-08 22:10:41.270 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-08 22:10:41.281 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-08-08 22:10:41.281 [info] trigger 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md resolve  [object Object] 
2025-08-08 22:10:41.281 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-08-08 22:10:45.302 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-08 22:10:45.312 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-08 22:10:45.314 [info] index finished after resolve  [object Object] 
2025-08-08 22:10:45.314 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:10:45.317 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-08-08 22:10:45.330 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-08-08 22:10:45.331 [info] index finished after resolve  [object Object] 
2025-08-08 22:10:45.331 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:10:45.333 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md   
2025-08-08 22:10:45.359 [info] trigger 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md resolve  [object Object] 
2025-08-08 22:10:45.360 [info] index finished after resolve  [object Object] 
2025-08-08 22:10:45.360 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:10:45.363 [debug] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-08-08 22:10:45.388 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-08-08 22:10:45.389 [info] index finished after resolve  [object Object] 
2025-08-08 22:10:45.389 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:10:52.171 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/激活函数.md   
2025-08-08 22:10:52.177 [info] trigger 学习库/Anki/Artificial Intelligence/激活函数.md resolve  [object Object] 
2025-08-08 22:10:52.178 [info] index finished after resolve  [object Object] 
2025-08-08 22:10:52.179 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:10:56.572 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/激活函数.md   
2025-08-08 22:10:56.575 [info] trigger 学习库/Anki/Artificial Intelligence/激活函数.md resolve  [object Object] 
2025-08-08 22:10:56.576 [info] index finished after resolve  [object Object] 
2025-08-08 22:10:56.576 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:11:07.149 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/激活函数.md   
2025-08-08 22:11:07.155 [info] trigger 学习库/Anki/Artificial Intelligence/激活函数.md resolve  [object Object] 
2025-08-08 22:11:07.156 [info] index finished after resolve  [object Object] 
2025-08-08 22:11:07.156 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:11:19.042 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/激活函数.md   
2025-08-08 22:11:19.048 [info] trigger 学习库/Anki/Artificial Intelligence/激活函数.md resolve  [object Object] 
2025-08-08 22:11:19.049 [info] index finished after resolve  [object Object] 
2025-08-08 22:11:19.049 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:11:24.262 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/激活函数.md   
2025-08-08 22:11:24.268 [info] trigger 学习库/Anki/Artificial Intelligence/激活函数.md resolve  [object Object] 
2025-08-08 22:11:24.272 [info] index finished after resolve  [object Object] 
2025-08-08 22:11:24.272 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:12:15.366 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/激活函数.md   
2025-08-08 22:12:15.371 [info] trigger 学习库/Anki/Artificial Intelligence/激活函数.md resolve  [object Object] 
2025-08-08 22:12:15.371 [info] index finished after resolve  [object Object] 
2025-08-08 22:12:15.372 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:12:35.086 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 22:12:35.246 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 22:12:35.248 [info] index finished after resolve  [object Object] 
2025-08-08 22:12:35.248 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:12:38.766 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/激活函数.md   
2025-08-08 22:12:38.773 [info] trigger 学习库/Anki/Artificial Intelligence/激活函数.md resolve  [object Object] 
2025-08-08 22:12:38.774 [info] index finished after resolve  [object Object] 
2025-08-08 22:12:38.774 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:13:06.732 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 22:13:07.272 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 22:13:07.276 [info] index finished after resolve  [object Object] 
2025-08-08 22:13:07.277 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:13:10.624 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名 1.md   
2025-08-08 22:13:10.654 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 1.md resolve  [object Object] 
2025-08-08 22:13:10.655 [info] index finished after resolve  [object Object] 
2025-08-08 22:13:10.655 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:13:16.012 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名 1.md   
2025-08-08 22:13:16.108 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 1.md resolve  [object Object] 
2025-08-08 22:13:16.113 [info] index finished after resolve  [object Object] 
2025-08-08 22:13:16.113 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:13:24.336 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名 1.md   
2025-08-08 22:13:24.424 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 1.md resolve  [object Object] 
2025-08-08 22:13:24.426 [info] index finished after resolve  [object Object] 
2025-08-08 22:13:24.427 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:13:44.386 [info] refresh page data from rename listeners 0 1072   
2025-08-08 22:13:54.628 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 22:13:54.908 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 22:13:54.908 [info] index finished after resolve  [object Object] 
2025-08-08 22:13:54.908 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:13:57.253 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 22:13:57.531 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 22:13:57.533 [info] index finished after resolve  [object Object] 
2025-08-08 22:13:57.533 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:14:46.696 [info] components database created cost 1 ms   
2025-08-08 22:14:46.696 [info] components index initializing...   
2025-08-08 22:14:47.187 [info] start to batch put pages: 5   
2025-08-08 22:14:47.190 [info] batch persist cost 5  3 
2025-08-08 22:14:47.275 [info] components index initialized, 1072 files cost 580 ms   
2025-08-08 22:14:47.276 [info] refresh page data from init listeners 0 1072   
2025-08-08 22:14:48.962 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 22:14:49.278 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 22:14:49.674 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-08 22:14:49.678 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-08 22:14:49.683 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-08 22:14:49.688 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-08 22:15:07.837 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 22:15:07.975 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 22:15:07.976 [info] index finished after resolve  [object Object] 
2025-08-08 22:15:07.977 [info] refresh page data from resolve listeners 0 1072   
2025-08-08 22:16:15.769 [info] components database created cost 0 ms   
2025-08-08 22:16:15.770 [info] components index initializing...   
2025-08-08 22:16:16.008 [info] start to batch put pages: 5   
2025-08-08 22:16:16.040 [info] batch persist cost 5  32 
2025-08-08 22:16:16.083 [info] components index initialized, 1072 files cost 314 ms   
2025-08-08 22:16:16.083 [info] refresh page data from init listeners 0 1072   
2025-08-08 22:16:17.635 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 22:16:18.031 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 22:16:18.477 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-08 22:16:18.481 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-08 22:16:18.491 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-08 22:16:18.496 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-08 22:22:01.713 [info] components database created cost 1 ms   
2025-08-08 22:22:01.715 [info] components index initializing...   
2025-08-08 22:22:01.957 [info] start to batch put pages: 5   
2025-08-08 22:22:01.958 [info] batch persist cost 5  1 
2025-08-08 22:22:02.023 [info] components index initialized, 1072 files cost 311 ms   
2025-08-08 22:22:02.023 [info] refresh page data from init listeners 0 1072   
2025-08-08 22:22:02.759 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 22:22:03.140 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-08 22:22:04.123 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-08 22:22:04.140 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-08 22:22:04.151 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-08 22:22:04.156 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-08 22:23:20.660 [info] indexing created file 学习库/Anki/Artificial Intelligence/未命名.md  [object Object] 
2025-08-08 22:23:20.660 [info] indexing created ignore file 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-08 22:23:20.721 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-08 22:23:20.738 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-08 22:23:20.739 [info] index finished after resolve  [object Object] 
2025-08-08 22:23:20.740 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:23:23.991 [info] refresh page data from rename listeners 0 1073   
2025-08-08 22:23:23.996 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-08 22:23:26.974 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:23:26.986 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:23:26.987 [info] index finished after resolve  [object Object] 
2025-08-08 22:23:26.988 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:23:31.717 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:23:31.722 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:23:31.722 [info] index finished after resolve  [object Object] 
2025-08-08 22:23:31.723 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:23:36.786 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:23:36.791 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:23:36.792 [info] index finished after resolve  [object Object] 
2025-08-08 22:23:36.792 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:23:39.496 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:23:39.500 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:23:39.501 [info] index finished after resolve  [object Object] 
2025-08-08 22:23:39.502 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:23:40.019 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:23:40.024 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:23:40.025 [info] index finished after resolve  [object Object] 
2025-08-08 22:23:40.026 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:23:41.288 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:23:41.291 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:23:41.292 [info] index finished after resolve  [object Object] 
2025-08-08 22:23:41.292 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:24:33.753 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:24:33.783 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:24:33.787 [info] index finished after resolve  [object Object] 
2025-08-08 22:24:33.788 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:24:50.986 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:24:50.992 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:24:50.993 [info] index finished after resolve  [object Object] 
2025-08-08 22:24:50.993 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:22.430 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:25:22.443 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:25:22.444 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:22.444 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:25.180 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:25:25.188 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:25:25.189 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:25.189 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:27.451 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:25:27.463 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:25:27.465 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:27.466 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:43.801 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:25:43.832 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:25:43.834 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:43.834 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:47.790 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:25:47.811 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:25:47.813 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:47.813 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:50.121 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:25:50.142 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:25:50.144 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:50.145 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:52.524 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:25:52.558 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:25:52.560 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:52.560 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:54.612 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:25:54.638 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:25:54.639 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:54.639 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:56.754 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:25:56.778 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:25:56.779 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:56.779 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:25:58.800 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:25:58.807 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:25:58.807 [info] index finished after resolve  [object Object] 
2025-08-08 22:25:58.808 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:26:00.894 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:26:00.921 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:26:00.922 [info] index finished after resolve  [object Object] 
2025-08-08 22:26:00.923 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:26:03.299 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:26:03.325 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:26:03.325 [info] index finished after resolve  [object Object] 
2025-08-08 22:26:03.325 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:26:22.223 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:26:22.235 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:26:22.237 [info] index finished after resolve  [object Object] 
2025-08-08 22:26:22.237 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:26:54.590 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:26:54.597 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:26:54.598 [info] index finished after resolve  [object Object] 
2025-08-08 22:26:54.599 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:26:56.624 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:26:56.632 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:26:56.632 [info] index finished after resolve  [object Object] 
2025-08-08 22:26:56.633 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:26:58.768 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:26:58.776 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:26:58.780 [info] index finished after resolve  [object Object] 
2025-08-08 22:26:58.781 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:00.951 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:00.958 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:00.960 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:00.960 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:03.031 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:03.039 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:03.040 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:03.040 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:05.301 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:05.309 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:05.310 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:05.310 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:07.299 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:07.307 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:07.313 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:07.314 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:39.961 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:39.971 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:39.972 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:39.972 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:42.374 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:42.381 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:42.383 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:42.383 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:50.335 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:50.342 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:50.343 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:50.343 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:27:58.666 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:27:58.674 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:27:58.676 [info] index finished after resolve  [object Object] 
2025-08-08 22:27:58.676 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:28:00.894 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:28:00.903 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:28:00.904 [info] index finished after resolve  [object Object] 
2025-08-08 22:28:00.904 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:28:03.711 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:28:03.716 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:28:03.718 [info] index finished after resolve  [object Object] 
2025-08-08 22:28:03.719 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:28:21.297 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:28:21.308 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:28:21.310 [info] index finished after resolve  [object Object] 
2025-08-08 22:28:21.310 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:28:23.674 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:28:23.682 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:28:23.683 [info] index finished after resolve  [object Object] 
2025-08-08 22:28:23.683 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:28:25.781 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:28:25.789 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:28:25.790 [info] index finished after resolve  [object Object] 
2025-08-08 22:28:25.790 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:28:27.963 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:28:27.977 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:28:27.979 [info] index finished after resolve  [object Object] 
2025-08-08 22:28:27.979 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:28:31.470 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:28:31.480 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:28:31.481 [info] index finished after resolve  [object Object] 
2025-08-08 22:28:31.481 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:21.775 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:21.783 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:21.795 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:21.795 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:23.795 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:23.802 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:23.803 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:23.803 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:25.875 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:25.888 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:25.890 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:25.890 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:29.798 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:29.810 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:29.812 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:29.812 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:31.832 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:31.839 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:31.840 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:31.840 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:35.967 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:35.980 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:35.981 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:35.981 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:38.206 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:38.214 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:38.215 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:38.215 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:40.341 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:40.348 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:40.350 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:40.350 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:42.380 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:42.390 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:42.391 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:42.391 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:29:45.871 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:29:45.878 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:29:45.879 [info] index finished after resolve  [object Object] 
2025-08-08 22:29:45.879 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:30:21.164 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:30:21.175 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:30:21.176 [info] index finished after resolve  [object Object] 
2025-08-08 22:30:21.177 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:30:26.192 [debug] ignore file modify evnet 学习库/Deep learning/概念库/神经网络的调参.md   
2025-08-08 22:30:26.201 [info] trigger 学习库/Deep learning/概念库/神经网络的调参.md resolve  [object Object] 
2025-08-08 22:30:26.203 [info] index finished after resolve  [object Object] 
2025-08-08 22:30:26.203 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:32:28.514 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:32:28.519 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:32:28.522 [info] index finished after resolve  [object Object] 
2025-08-08 22:32:28.522 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:32:42.585 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:32:42.612 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:32:42.613 [info] index finished after resolve  [object Object] 
2025-08-08 22:32:42.613 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:32:59.402 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:32:59.430 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:32:59.431 [info] index finished after resolve  [object Object] 
2025-08-08 22:32:59.431 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:33:14.392 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:33:14.400 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:33:14.401 [info] index finished after resolve  [object Object] 
2025-08-08 22:33:14.402 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:33:32.306 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:33:32.358 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:33:32.363 [info] index finished after resolve  [object Object] 
2025-08-08 22:33:32.364 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:33:39.108 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:33:39.135 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:33:39.136 [info] index finished after resolve  [object Object] 
2025-08-08 22:33:39.136 [info] refresh page data from resolve listeners 0 1073   
2025-08-08 22:33:49.215 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/调参.md   
2025-08-08 22:33:49.244 [info] trigger 学习库/Anki/Artificial Intelligence/调参.md resolve  [object Object] 
2025-08-08 22:33:49.245 [info] index finished after resolve  [object Object] 
2025-08-08 22:33:49.245 [info] refresh page data from resolve listeners 0 1073   

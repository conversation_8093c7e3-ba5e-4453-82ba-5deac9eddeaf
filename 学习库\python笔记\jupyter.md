---
tags:
  - 学习
  - python
  - jupyter
---

## Ju<PERSON><PERSON> 和 vscode 联合使用

- **创建和管理环境**：在你的项目文件夹下，使用命令行通过 uv 创建一个虚拟环境，并安装Jupyter和其他你需要的库（如 pandas、matplotlib 等）

```python
 # 创建一个虚拟环境
uv init

# 激活环境
uv sync

# 安装 jupyter 和其他包
uv add jupyter pandas matplotlib
```

- **在 VS Code 中工作**：打开 VS Code，并安装 Jupyter 扩展
- **连接环境与编辑器**：在 VS Code 中打开一个 .ipynb 文件。点击右上角的 "Select Kernel"（选择内核），然后选择你刚才用 uv 创建的那个虚拟环境 (.venv)



---
```ad-summary
title: 相关连接
[[项目管理]]
```
---
tags:
  - 学习
  - deep_learning
---
# 神经网络的调参

模型参数的训练实际上就是一个不断迭代，寻找到一个方程  $H_{\theta }(X)$ 来拟合数据集。然而到这里，我们只知道需要去拟合训练集，但拟合的最佳程度我们并没有讨论过。看看下面**回归模型**的拟合程度，看看能发现什么。
![[正则化-2025-04-24-15-42-07.png]]

- 最左边的图：拟合程度比较低，连训练集的准确率如此低，那么测试集肯定也不高，也就是模型的泛化能力不高。
- 最右边的图：拟合程度非常高，甚至每一个点都能通过 $H_{\theta }(X)$ 来表达，但这并不是我们想要的，因为数据集中无法避免的存在着许多噪声，而在理想情况下，我们希望噪声对我们的模型训练的影响为 0。而如果模型将训练集中每一个点都精准描述出来，显然包含了许许多多噪声点，在后续的测试集中得到的准确率也不高。另一方面，太过复杂的 $H_{\theta }(X)$ 导致函数形状并不平滑，而会像图中那样拐来拐去，并不能起到预测的作用，“回归”模型也丧失了其**预测能力**（也就是模型**泛化能力**）
- 中间的图：$H_{\theta }(X)$ 不过于复杂或过于简单，并且能够直观的预测函数的走向。虽然它在测试集的中准确列不及图三高，但在测试集中得到的准确率是最高的，同时泛化能力也是最强的。

同样，在**分类模型**中也存在过拟合与欠拟合的情况。
![[正则化-2025-04-24-15-48-52.png]]


|                                                            | <center>原因</center>                        | <center>解决方法</center>                                                       |
| ---------------------------------------------------------- | ------------------------------------------ | --------------------------------------------------------------------------- |
| <center>**欠拟合**<br>泛化能力差，训练样本集准确率高，测试样本集准确率低。</center><br> | 1. 训练样本数量少<br>2. 模型复杂度过低<br>3. 参数还未收敛就停止循环 | 1. 增加样本数量<br>2. 增加模型参数，提高模型复杂度<br>3. 增加循环次数<br>4. 查看是否是学习率过高导致模型无法收敛        |
| <center>**过拟合**<br>泛化能力差，训练样本集准确率低，测试样本集准确率低。</center><br> | 1. 数据噪声太大<br>2. 特征太多<br>3. 模型太复杂           | 1. 清洗数据<br>2. 减少模型参数，降低模型复杂度<br>3. 增加惩罚因子（正则化），保留所有的特征，但是减少参数的大小（magnitude） |

- 欠拟合：泛化能力差，训练样本集准确率低，测试样本集准确率低。
	- 欠拟合原因：
		- 训练样本数量少
		- 模型复杂度过低
		- 参数还未收敛就停止循环
	- 欠拟合的解决办法：
		- 增加样本数量
		- 增加模型参数，提高模型复杂度
		- 增加循环次数
		- 查看是否是学习率过高导致模型无法收敛
- 过拟合：泛化能力差，训练样本集准确率高，测试样本集准确率低。
	- 过拟合原因：
		- 数据噪声太大
		- 特征太多
		- 模型太复杂
	- 过拟合的解决办法：
		- 清洗数据
		- 减少模型参数，降低模型复杂度
		- 增加惩罚因子（正则化），保留所有的特征，但是减少参数的大小（magnitude）
- 合适的拟合程度：泛化能力强，训练样本集准确率高，测试样本集准确率高

## 正则化

Regularization，中文翻译过来可以称为**正则化**，或者是**规范化**。什么是规则？闭卷考试中不能查书，这就是规则，一个**限制**。同理，正则化就是说给**损失函数**加上一些限制，因为神经网络训练过程中通过损失函数的变化来调整参数（$\omega$ 和 $b$）的变化，所以如果损失函数变大了，那么**参数反而不会过度的增长**。
![](./attachments/神经网络的调参-2025-08-08,22-08,22-01-29.gif)

**正则化**就是一系列用于**防止模型过拟合、提高模型泛化能力**的技术的总称。它的核心思想是在模型的损失函数（Loss Function）中引入一个额外的**惩罚项（Penalty Term）**，从而来抑制参数的变化。
$$新损失函数 = 原始损失函数 + λ * 惩罚项$$

- **原始损失函数 (Original Loss Function)**：衡量模型在训练数据上的预测误差，例如均方误差（MSE）或交叉熵（Cross-Entropy）。
- **λ (正则化系数或惩罚系数)**：这是一个**超参数**，需要我们手动调整。控制正则化惩罚的强度：
    - λ = 0：没有正则化，模型可能过拟合。
    - λ 很大：惩罚项占比过大，模型可能无法充分拟合训练数据，导致欠拟合（Underfitting）。
    - 合适的 λ：在拟合训练数据和保持模型简单性之间取得平衡，提高泛化能力。

```ad-note
title: L1正则化
- **惩罚项**：模型所有权重（coefficients）的**绝对值之和**乘以 $λ$。
    - 惩罚项 = $λ * \sum_{i=1}^N|w_i|$ （$wᵢ$ 是模型的第 $i$个权重），**L1范数也就指的是绝对值之和**
- **效果**：L1 正则化倾向于产生**稀疏(sparse)** 的权重矩阵，使得模型中一部分权重的值**精确地变为0** 
- **优点**：
    - 可以有效地进行**特征选择（Feature Selection）**，将不重要的特征对应的权重置为 0，简化模型。
    - 在特征数量很多的情况下特别有用。
- **缺点**：
    - 如果存在高度相关的特征，Lasso 倾向于随机选择其中一个特征，而将其他相关特征的权重置为 0，可能丢失一些信息。
    - 惩罚项在 0 点处不可导，需要特殊的优化算法（如坐标下降法）。
```

```ad-note
title: L2正则化
- **惩罚项**：模型所有权重的**平方和**乘以 $λ$。
    - 惩罚项 =$λ * \sum_{i=1}^{N}wᵢ²$，**L2范数指的是平方和的平方根**
- **效果**：L2 正则化倾向于使模型的权重**趋近于 0，但通常不会精确等于 0**。使权重的值较小且分散。
- **优点**：
    - 计算上更简单（惩罚项处处可导），优化过程更稳定。
    - 在处理特征之间存在多重共线性（multicollinearity）时表现较好，能有效减小权重值。
    - 通常能提供更好的预测性能，泛化能力强。
- **缺点**：
    - 不会进行显式的特征选择，保留所有特征，模型的可解释性不如 L1。
```


## 早停法（Early Stopping）
```ad-note
title: 早停法
- **机制**：在训练过程中，除了监控训练损失，同时监控模型在 **验证集（Validation Set)** 上的性能（如验证损失或准确率）。当验证集上的性能不再提升甚至开始下降时，就**停止训练**，即使训练损失仍在下降。
- **效果**：防止模型在训练数据上过度拟合，通过在验证性能最佳的点停止训练来获得更好的泛化能力。
- **优点**：实现简单，效果显著，计算开销小。
- **缺点**：需要一个独立的验证集；可能过早停止，导致模型未充分训练
```


## 数据增强 (Data Augmentation)
```ad-note
title: 数据增强
- **机制**：通过对现有训练数据进行各种**变换**（如旋转、缩放、裁剪、翻转图像；添加噪声到音频；改变文本语序等）来**人工增加训练数据量和多样性**。
- **效果**：让模型看到更多样化的数据，从而学习到更具不变性的特征，提高对新数据的适应能力，间接起到了正则化的作用。
- **优点**：非常有效，特别是在数据量有限的情况下（如图像识别、语音识别）。
- **缺点**：需要根据数据类型设计合理的增强策略，不当的增强可能引入噪声或改变数据本质。
```


## 批量标准化 (Batch Normalization)
```ad-note
title: 批量标准化
- **机制**：在神经网络的每一层**激活函数之前**，对该层的输入（或输出）进行标准化处理，使其均值为 0，方差为 1。
- **效果**：
    - 加速模型训练收敛速度。
    - 使得网络对权重初始化不那么敏感。
    - 具有一定的**正则化效果**，因为每个 mini-batch 的均值和方差带有一定的随机性，给网络的激活值引入了噪声，类似于 Dropout。有时可以减少甚至替代 Dropout 的使用
```


## Dropout (随机失活)
```ad-note
title: Dropout
- **机制**：为了**防止**在模型训练过程中，**过于依赖某些参数**，从而导致过拟合，因此**随机**地将一部分神经元的输出**暂时**设置为 0（即“失活”）。每次训练迭代（或每个 mini-batch）都会随机选择不同的神经元失活。
- **效果**：
    - 迫使网络学习更加**鲁棒(robust)** 的特征，因为神经元不能依赖于其他特定神经元的存在。
    - 可以看作是同时训练多个共享权重的、结构不同的“瘦”网络，并在测试时进行近似的模型平均（ensemble averaging）。
    - 有效防止神经网络的过拟合。
- **注意**：Dropout 只在训练阶段使用，在测试（推理）阶段需要关闭 Dropout，并通常将所有神经元的权重乘以保持概率（1 - dropout rate）来进行缩放，以补偿训练时的失活。
```

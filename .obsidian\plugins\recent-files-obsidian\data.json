{"recentFiles": [{"basename": "Attention", "path": "学习库/Deep learning/概念库/Attention.md"}, {"basename": "Transformer", "path": "学习库/Deep learning/概念库/Transformer.md"}, {"basename": "10. 循环神经网络（Recurrent Neural Network）", "path": "学习库/Deep learning/pytorch/10. 循环神经网络（Recurrent Neural Network）.md"}, {"basename": "Token", "path": "学习库/Artificial Intelligence/Token.md"}, {"basename": "concatenate和add", "path": "学习库/Deep learning/概念库/concatenate和add.md"}, {"basename": "基本概念", "path": "学习库/An<PERSON>/Artificial Intelligence/基本概念.md"}, {"basename": "Home", "path": "Home/Home.md"}, {"basename": "词嵌入（word embedding）.excalidraw", "path": "学习库/Deep learning/概念库/excalidraw/词嵌入（word embedding）.excalidraw.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}
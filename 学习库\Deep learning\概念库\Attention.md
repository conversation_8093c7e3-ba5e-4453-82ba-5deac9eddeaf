# 什么是 Attention
我们在获取信息的时候，通常是先从宏观上建立一个比较模糊的认识，然后再花费更多的注意力去进行细节上的观察，学习和思考。
<span style="background:#d3f8b6"><font color="#c00000">attention 可以帮助模型对输入的 x 每个部分赋予不同的权重，抽出更加关键及重要的信息，使模型做出更准确的判断，同时不会对模型的计算和存储带来更大的开销。</font></span>
## 视觉任务
比如我想要判断以下这张图片是在什么地方
![[Pasted image 20240706093227.png]]
我们是不是会先宏观的看这张图片，这张图片上的街道很拥挤，有很多的车和人，并且灯火通明的，然后我们就会深入的去注意这张图片上的细节，比如它的广告牌上写的是繁体字，它的街道很狭窄，它的巴士是双层巴士，可以预测这张图片可能是香港的某个街道。
## 自然语言处理
有以下这个句子，如果我问你这个句子中的 it 指代的是什么呢？
>The animal didn't cross the street because it was too tired.

在这个句子中，
- query = 查询 it
- key = 关键词（如 animal, street 等）
- value = 答案（it 是什么）
![[Pasted image 20240706101124.png]]
第一次看到这个问题，可能会想 it 应该指代的是前面的第一个名词 street，此时我们会认为 key = value = street。
第二次经过对细节的思考，比如看到后面的 tired 这个 key，query 想表达的意思是 it 这个东西 tired 了，我们就会想 street 是不会 tired，所以 value 不可能是 street，观察整个句子只有 animal 会 tired，所以最终这个 it 的 value 就是 animal。
## 推荐系统
我是淘宝的运营，我想要推荐给客户商品广告来诱导其进行消费我应该怎么做？
![[Pasted image 20240706095850.png]]
我可以把用户都购买历史用一个序列记录下来，我可以观察他购买最多的东西是什么，是吃的，穿的，还是用的；然后我再继续研究，她买的穿的都是什么类型的，价位是多少，重复购买的次数是多少来综合判断该推送什么广告给她。
# Attention 是如何工作的
## 理解 attention
对于摩尔（mole），在不同的英语语境中有不同的意思，比如：
- American sherw mole (美国==鼹鼠==)
- One mole of carbon dioxide (一==摩尔==二氧化碳)
- Take a biopsy of the mole（对==痣==进行活组织切片检查）
但 transformer 在将文本分成一个一个独立的 token 的时候，这三个 mole 对应的都是同一个向量。
![[Pasted image 20241008110538.png|0x0]]

而 attention 的作用就是通过周围的信息将给予这些向量不同的权重，使它对应到特定的语义中![[GIF 2024-10-8 11-09-20.gif]]

## Single head of Attention
在一个语句中的所有词实际上是包含了位置信息的高维向量（嵌入向量），这些向量只编码了该单词的含义，和上下文没有关联。通过 attention 可以转换为更加精准的表示经过形容词修饰过的名词所对应的向量。
![[GIF 2024-10-8 14-54-29.gif]]
### 查询向量（query）
attention 的查询（query）过程就像是每个名词，比如 creature，在询问：“我前面有形容词嘛？”，然后 creature 前面的 fluffy 和 blue 就回答：“我就是”，这个询问的过程被编辑成查询向量（query）。
得到查询向量首先需要先将嵌入向量左乘一个查询矩阵 $W_Q$ ，将每个嵌入向量都进行同样的操作就可以得到每个 token 的 Q
![[Pasted image 20241008150458.png]]
### 键向量 （key）
从概念上来说可以把 key 堪称 query 的回答，同样得到 key 需要将嵌入向量左乘一个键矩阵 $W_k$，从而将嵌入向量映射到相同的低纬度空间，当得到的 key 和 query 方向相对齐的时候，就能认为它们是匹配的
![[Pasted image 20241008151321.png]]
当 key 和 query 直接进行点积操作，那么关联越大的词就是较大的正数，也就是说 key（fluffy 和 blue）注意到了 query（creature）的嵌入。如果是两关联不大的词进行点积，那么得到的就是较小的数值，这表明这两个词互补相关联。再通过 [[Softmax函数]]进行计算，得到各个词之间相关联的概率。
![[Pasted image 20241008152233.png]]
### 值向量 （Value）
值向量（value）可以理解为从原始的嵌入向量得到目标的嵌入向量需要加上什么向量呢？这个向量就是值向量。value 同样需要将嵌入向量左乘值矩阵 $W_v$

![[Pasted image 20241008153216.png|610x370]]

## 数学方法
判断什么东西更重要，转换成数学的方法其实就是重要度计算，或者相似度计算。
```ad-hint
attention 中的 q(query), k (key), v (value)。假设我们正在进行中英文翻译任务，其中输入的中文句子为："今天天气很好。"，我们希望生成对应的英文句子："The weather is very nice today."
- **Key（K）**：对于中文句子中的每个词，都会有一个Key向量，比如 "今天"、"天气"、"很好" 的Key向量，用来表示它们的关键信息。
- **Query（Q）**：在生成英文句子时，我们会有一个Query向量，它决定了我们当前关注的是哪些中文词语或者信息，比如在翻译 "很好" 时，Query向量会包含对应的英文信息。
- **Value（V）**：每个中文词语都有一个对应的Value向量，比如 "今天" 对应的是 "today"，"天气" 对应的是 "weather"，"很好" 对应的是 "nice"（也就是说v是与k相关的向量）。
在注意力机制中，通过计算Query向量与Key向量的相关性，然后将相关性作为权重应用于对应的Value向量，以实现高效的信息关联和转换，从而完成翻译任务。

```
通过点乘的方法计算Q 和 K 里的每一个事物的相似度，就可以拿到 Q 和k1的相似值s1，Q 和k2的相似值s2，Q 和k4的相似值 s4，随后再经过一层 [[学习库/Deep learning/概念库/激活函数#softmax|softmax函数]]，可以得到相似度的百分比（概率）：a1, a2, a3, a4。
k 一般是等于 v 的，因为由上一步计算得到 a1, a2, a3, a4 是没有考虑词向量本身的特征的，所以在图中的阶段 3 是再将得到的 a 与 v 进行乘积求和得到这个词和整句话中其它词的关系
![[Pasted image 20240706151929.png]]


---
tags:
  - 学习
  - token
  - Artifical_Intelligence
---
# Token

```ad-note
Token 是模型用来处理文本的最小单位。它可以是一个完整的词，也可以是一个词的一部分（子词，subword），甚至是一个标点符号或一个字母。
```

---
## 为什么使用 Token 而不是使用 Word?

- **处理未知词 (Out-of-Vocabulary, OOV):** 如果词汇表里没有 "embedding" 这个词，但有 "embed" 和 "##ding" (##表示是词的后续部分)，模型就可以通过组合这两个token来理解 "embedding"。这大大增强了模型处理新词和罕见词的能力。
- **控制词汇表大小:** 使用子词切分（Subword Tokenization）可以有效地用一个相对较小的词汇表（比如几万个token）来表示几乎所有的词语，避免了独热编码那样的维度灾难。
- **捕捉构词法:** 像 "running", "ran", "runner" 这些词可以共享 "run" 这个token，模型更容易学习到它们之间的形态学关系。

```ad-example
title: 举例
对于句子: "Let's learn about tokenization."
一个典型的分词器（Tokenizer）可能会把它切分成如下的tokens：

`["Let", "'s", "learn", "about", "token", "ization", "."]`

其中的`"tokenization"` 被拆分成了 `"token"` 和 `"ization"`。这里的每一个字符串就是一个 **token**。
```
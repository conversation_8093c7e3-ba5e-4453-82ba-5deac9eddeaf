---
tags:
  - 学习
  - deep_learning
---
# 独热编码（One-Hot Encoding）

独热编码（One-Hot Encoding）是一种将**分类变量转换为数值型变量**的常用方法。它将每个类别转换为一个二进制向量，其中只有一个元素为1，其余元素为0。独热编码的优点是可以避免模型误解类别之间的顺序关系，同时保留了类别信息。

## 为什么需要它

机器学习模型通常要求输入数据为数值型，如果有像“颜色”（例如“红色”、“绿色”、“蓝色”）这样的分类变量，不能直接将这些文本标签输入到模型中。
一个简单的想法是给每个类别分配一个整数值，例如“红色”=0，“绿色”=1，“蓝色”=2。但是，这样做会引入一个问题：模型可能会误解这些整数值之间的顺序关系（例如，模型可能会认为“绿色”比“红色”更大）。
独热编码通过将每个类别转换为一个二进制向量来解决这个问题。这样，模型就不会误解类别之间的顺序关系了。

## 如何进行独热编码

1. 确定类别：找出给分类特征所有可能的唯一值（类别）。
2. 创建新特征：为每一个唯一类别创建一个新的二进制特征（列）。
3. 赋值：对于原始数据中的每一行（每个样本），在其对应类别的新特征中填1，在为其它类别的新特征中填0。

```ad-example
title: 举例
collapse:close
假设有一个“颜色”特征，包含以下数据：

| 样本ID | 颜色  |
| ---- | --- |
| 1    | 红色  |
| 2    | 绿色  |
| 3    | 蓝色  |
| 4    | 红色  |

然后根据独热编码的步骤：
1. 确定类别：红色、绿色、蓝色（共3个类别）。
2. 创建新特征：创建3个新特征，分别表示颜色_红色、颜色_绿色、颜色_蓝色。
3. 赋值

| 样本ID | 颜色_红色 | 颜色_绿色 | 颜色_蓝色 |
| ---- | ----- | ----- | ----- |
| 1    | 1     | 0     | 0     |
| 2    | 0     | 1     | 0     |
| 3    | 0     | 0     | 1     |
| 4    | 1     | 0     | 0     |

现在每一行都变成了一个二进制向量，其中只有一个元素为1（表示该样本的颜色），其余元素为0。这样，模型就可以处理这些数据而不会误解类别之间的顺序关系了。
```

## 在分类任务中的应用

目标标签编码（用于特定损失函数）： 在多类分类任务中，如果你使用的损失函数是分类交叉熵 (Categorical Cross-Entropy)，那么你需要将你的真实标签（例如，“猫”、“狗”、“鸟”）也进行独热编码。
````ad-example
title: 分类的独热编码
color:178,22,164
原始类别为：猫（0），狗（1），鸟（2）
独热编码后的类别：
- 猫：[1, 0, 0]
- 狗：[0, 1, 0]
- 鸟：[0, 0, 1]
````

## 独热编码的缺点

因为独热编码中每个词对应的向量都是一个二进制的向量，词和词之间没有语义间的联系
![](./attachments/独热编码(One-Hot%20Encoding)-2025-08-09,09-08,09-58-42.png)